// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		BEC566920761D90300A33029 /* All */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 001B599808BDB826006539E9 /* Build configuration list for PBXAggregateTarget "All" */;
			buildPhases = (
			);
			dependencies = (
				F3E1F8032A78C3C500AC76D3 /* PBXTargetDependency */,
				F3E1F8012A78C3BE00AC76D3 /* PBXTargetDependency */,
				F3E1F7FF2A78C3AD00AC76D3 /* PBXTargetDependency */,
				F35E56E72983133F00A43A5F /* PBXTargetDependency */,
				DB0F490517CA5249008798C5 /* PBXTargetDependency */,
				DB0F490717CA5249008798C5 /* PBXTargetDependency */,
				DB166E9816A1D7CF00A1396C /* PBXTargetDependency */,
				DB166E9616A1D7CD00A1396C /* PBXTargetDependency */,
				DB166E6C16A1D72000A1396C /* PBXTargetDependency */,
				DB166E5616A1D6B800A1396C /* PBXTargetDependency */,
				DB166E3B16A1D65A00A1396C /* PBXTargetDependency */,
				DB166E2016A1D5D000A1396C /* PBXTargetDependency */,
				DB166E0916A1D5A400A1396C /* PBXTargetDependency */,
				DB166DF216A1D53700A1396C /* PBXTargetDependency */,
				DB166DD916A1D38900A1396C /* PBXTargetDependency */,
				001799481074403E00F5D044 /* PBXTargetDependency */,
				0017994C1074403E00F5D044 /* PBXTargetDependency */,
				001799501074403E00F5D044 /* PBXTargetDependency */,
				001799521074403E00F5D044 /* PBXTargetDependency */,
				0017995A1074403E00F5D044 /* PBXTargetDependency */,
				0017995E1074403E00F5D044 /* PBXTargetDependency */,
				001799601074403E00F5D044 /* PBXTargetDependency */,
				001799661074403E00F5D044 /* PBXTargetDependency */,
				001799681074403E00F5D044 /* PBXTargetDependency */,
				0017996A1074403E00F5D044 /* PBXTargetDependency */,
				0017996C1074403E00F5D044 /* PBXTargetDependency */,
				0017996E1074403E00F5D044 /* PBXTargetDependency */,
				001799701074403E00F5D044 /* PBXTargetDependency */,
				001799741074403E00F5D044 /* PBXTargetDependency */,
				001799761074403E00F5D044 /* PBXTargetDependency */,
				001799781074403E00F5D044 /* PBXTargetDependency */,
				0017997C1074403E00F5D044 /* PBXTargetDependency */,
				001799801074403E00F5D044 /* PBXTargetDependency */,
				001799841074403E00F5D044 /* PBXTargetDependency */,
				001799881074403E00F5D044 /* PBXTargetDependency */,
				0017998A1074403E00F5D044 /* PBXTargetDependency */,
				0017998C1074403E00F5D044 /* PBXTargetDependency */,
				0017998E1074403E00F5D044 /* PBXTargetDependency */,
				001799921074403E00F5D044 /* PBXTargetDependency */,
				001799941074403E00F5D044 /* PBXTargetDependency */,
				001799961074403E00F5D044 /* PBXTargetDependency */,
				0017999E1074403E00F5D044 /* PBXTargetDependency */,
				001799A21074403E00F5D044 /* PBXTargetDependency */,
				DB166D7016A1CEAF00A1396C /* PBXTargetDependency */,
				DB166D6E16A1CEAA00A1396C /* PBXTargetDependency */,
			);
			name = All;
			productName = "Build All";
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		001795901074216E00F5D044 /* testatomic.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017958F1074216E00F5D044 /* testatomic.c */; };
		001795B11074222D00F5D044 /* testaudioinfo.c in Sources */ = {isa = PBXBuildFile; fileRef = 001795B01074222D00F5D044 /* testaudioinfo.c */; };
		0017972810742FB900F5D044 /* testgl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017972710742FB900F5D044 /* testgl.c */; };
		0017974F1074315700F5D044 /* testhaptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017974E1074315700F5D044 /* testhaptic.c */; };
		001797721074320D00F5D044 /* testdraw.c in Sources */ = {isa = PBXBuildFile; fileRef = 001797711074320D00F5D044 /* testdraw.c */; };
		00179792107432FA00F5D044 /* testime.c in Sources */ = {isa = PBXBuildFile; fileRef = 00179791107432FA00F5D044 /* testime.c */; };
		001797B41074339C00F5D044 /* testintersections.c in Sources */ = {isa = PBXBuildFile; fileRef = 001797B31074339C00F5D044 /* testintersections.c */; };
		001797D41074343E00F5D044 /* testloadso.c in Sources */ = {isa = PBXBuildFile; fileRef = 001797D31074343E00F5D044 /* testloadso.c */; };
		001798161074359B00F5D044 /* testmultiaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 001798151074359B00F5D044 /* testmultiaudio.c */; };
		0017987F1074392D00F5D044 /* testnative.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017985A107436ED00F5D044 /* testnative.c */; };
		001798801074392D00F5D044 /* testnativecocoa.m in Sources */ = {isa = PBXBuildFile; fileRef = 0017985C107436ED00F5D044 /* testnativecocoa.m */; };
		001798BA10743A4900F5D044 /* testpower.c in Sources */ = {isa = PBXBuildFile; fileRef = 001798B910743A4900F5D044 /* testpower.c */; };
		001798FA10743E9200F5D044 /* testresample.c in Sources */ = {isa = PBXBuildFile; fileRef = 001798F910743E9200F5D044 /* testresample.c */; };
		0017991A10743F5300F5D044 /* testsprite.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017991910743F5300F5D044 /* testsprite.c */; };
		0017993C10743FEF00F5D044 /* testwm.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017993B10743FEF00F5D044 /* testwm.c */; };
		002F341809CA1C5B00EBEB88 /* testfile.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F341709CA1C5B00EBEB88 /* testfile.c */; };
		002F343709CA1F6F00EBEB88 /* testiconv.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F343609CA1F6F00EBEB88 /* testiconv.c */; };
		002F345409CA202000EBEB88 /* testoverlay.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F345209CA201C00EBEB88 /* testoverlay.c */; };
		002F347009CA20A600EBEB88 /* testplatform.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F346F09CA20A600EBEB88 /* testplatform.c */; };
		00794E6609D20865003FC8A1 /* sample.wav in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6209D20839003FC8A1 /* sample.wav */; };
		00794EF009D23739003FC8A1 /* utf8.txt in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6309D20839003FC8A1 /* utf8.txt */; };
		00794EF709D237DE003FC8A1 /* moose.dat in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5E09D20839003FC8A1 /* moose.dat */; };
		453774A5120915E3002F0F45 /* testshape.c in Sources */ = {isa = PBXBuildFile; fileRef = 453774A4120915E3002F0F45 /* testshape.c */; };
		66E88E8B203B778F0004D44E /* testyuv_cvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 66E88E8A203B778F0004D44E /* testyuv_cvt.c */; };
		A1A8594E2BC72FC20045DD6C /* testautomation_properties.c in Sources */ = {isa = PBXBuildFile; fileRef = A1A859482BC72FC20045DD6C /* testautomation_properties.c */; };
		A1A859502BC72FC20045DD6C /* testautomation_subsystems.c in Sources */ = {isa = PBXBuildFile; fileRef = A1A859492BC72FC20045DD6C /* testautomation_subsystems.c */; };
		A1A859522BC72FC20045DD6C /* testautomation_log.c in Sources */ = {isa = PBXBuildFile; fileRef = A1A8594A2BC72FC20045DD6C /* testautomation_log.c */; };
		A1A859542BC72FC20045DD6C /* testautomation_time.c in Sources */ = {isa = PBXBuildFile; fileRef = A1A8594B2BC72FC20045DD6C /* testautomation_time.c */; };
		AAF02FFA1F90092700B9A9FB /* SDL_test_memory.c in Sources */ = {isa = PBXBuildFile; fileRef = AAF02FF41F90089800B9A9FB /* SDL_test_memory.c */; };
		BBFC08D0164C6876003E6A99 /* testcontroller.c in Sources */ = {isa = PBXBuildFile; fileRef = BBFC088E164C6820003E6A99 /* testcontroller.c */; };
		BEC566B10761D90300A33029 /* checkkeys.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D10FFB30A2C7F000001 /* checkkeys.c */; };
		BEC566CB0761D90300A33029 /* loopwave.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4872006D84C97F000001 /* loopwave.c */; };
		BEC567010761D90300A33029 /* testerror.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4878006D85357F000001 /* testerror.c */; };
		BEC567290761D90400A33029 /* testthread.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D58FFB311A97F000001 /* testthread.c */; };
		BEC567430761D90400A33029 /* testkeys.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D6CFFB313437F000001 /* testkeys.c */; };
		BEC567500761D90400A33029 /* testlock.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D75FFB313BB7F000001 /* testlock.c */; };
		BEC567780761D90500A33029 /* testsem.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E487E006D86A17F000001 /* testsem.c */; };
		BEC567930761D90500A33029 /* testtimer.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4880006D86A17F000001 /* testtimer.c */; };
		BEC567AD0761D90500A33029 /* testver.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4882006D86A17F000001 /* testver.c */; };
		BEC567F00761D90600A33029 /* torturethread.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4887006D86A17F000001 /* torturethread.c */; };
		DB0F48EE17CA51F8008798C5 /* testdrawchessboard.c in Sources */ = {isa = PBXBuildFile; fileRef = DB0F48D717CA51D2008798C5 /* testdrawchessboard.c */; };
		DB0F490317CA5225008798C5 /* testfilesystem.c in Sources */ = {isa = PBXBuildFile; fileRef = DB0F48D817CA51D2008798C5 /* testfilesystem.c */; };
		DB166D9316A1D1A500A1396C /* SDL_test_assert.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8416A1D1A500A1396C /* SDL_test_assert.c */; };
		DB166D9416A1D1A500A1396C /* SDL_test_common.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8516A1D1A500A1396C /* SDL_test_common.c */; };
		DB166D9516A1D1A500A1396C /* SDL_test_compare.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8616A1D1A500A1396C /* SDL_test_compare.c */; };
		DB166D9616A1D1A500A1396C /* SDL_test_crc32.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8716A1D1A500A1396C /* SDL_test_crc32.c */; };
		DB166D9716A1D1A500A1396C /* SDL_test_font.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8816A1D1A500A1396C /* SDL_test_font.c */; };
		DB166D9816A1D1A500A1396C /* SDL_test_fuzzer.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8916A1D1A500A1396C /* SDL_test_fuzzer.c */; };
		DB166D9916A1D1A500A1396C /* SDL_test_harness.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8A16A1D1A500A1396C /* SDL_test_harness.c */; };
		DB166D9F16A1D1A500A1396C /* SDL_test_log.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D9016A1D1A500A1396C /* SDL_test_log.c */; };
		DB166DA016A1D1A500A1396C /* SDL_test_md5.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D9116A1D1A500A1396C /* SDL_test_md5.c */; };
		DB166DD716A1D37800A1396C /* testmessage.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CBD16A1C74100A1396C /* testmessage.c */; };
		DB166DDB16A1D42F00A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166DF016A1D52500A1396C /* testrelative.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CBF16A1C74100A1396C /* testrelative.c */; };
		DB166E0716A1D59400A1396C /* testrendercopyex.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC016A1C74100A1396C /* testrendercopyex.c */; };
		DB166E1E16A1D5C300A1396C /* testrendertarget.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC116A1C74100A1396C /* testrendertarget.c */; };
		DB166E2216A1D5EC00A1396C /* sample.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6109D20839003FC8A1 /* sample.bmp */; };
		DB166E2316A1D60B00A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166E2516A1D61900A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166E2616A1D61900A1396C /* sample.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6109D20839003FC8A1 /* sample.bmp */; };
		DB166E3C16A1D66500A1396C /* testrumble.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC216A1C74100A1396C /* testrumble.c */; };
		DB166E4D16A1D69000A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166E4E16A1D69000A1396C /* sample.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6109D20839003FC8A1 /* sample.bmp */; };
		DB166E5416A1D6A300A1396C /* testscale.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC316A1C74100A1396C /* testscale.c */; };
		DB166E6A16A1D70C00A1396C /* testshader.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC416A1C74100A1396C /* testshader.c */; };
		DB166E9316A1D7BC00A1396C /* testspriteminimal.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC516A1C74100A1396C /* testspriteminimal.c */; };
		DB166E9416A1D7C700A1396C /* teststreaming.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC616A1C74100A1396C /* teststreaming.c */; };
		DB166E9A16A1D7F700A1396C /* moose.dat in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5E09D20839003FC8A1 /* moose.dat */; };
		DB166E9C16A1D80900A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB445EFB18184BB600B306B0 /* testdropfile.c in Sources */ = {isa = PBXBuildFile; fileRef = DB445EFA18184BB600B306B0 /* testdropfile.c */; };
		DB89958418A19B130092407C /* testhotplug.c in Sources */ = {isa = PBXBuildFile; fileRef = DB89958318A19B130092407C /* testhotplug.c */; };
		F35E56CF2983130F00A43A5F /* testautomation_main.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56B62983130A00A43A5F /* testautomation_main.c */; };
		F35E56D02983130F00A43A5F /* testautomation_hints.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56B72983130A00A43A5F /* testautomation_hints.c */; };
		F35E56D12983130F00A43A5F /* testautomation_render.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56B82983130A00A43A5F /* testautomation_render.c */; };
		F35E56D22983130F00A43A5F /* testautomation_iostream.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56B92983130B00A43A5F /* testautomation_iostream.c */; };
		F35E56D32983130F00A43A5F /* testautomation_math.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56BA2983130B00A43A5F /* testautomation_math.c */; };
		F35E56D42983130F00A43A5F /* testautomation_events.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56BB2983130B00A43A5F /* testautomation_events.c */; };
		F35E56D52983130F00A43A5F /* testautomation_clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56BC2983130B00A43A5F /* testautomation_clipboard.c */; };
		F35E56D62983130F00A43A5F /* testautomation_timer.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56BD2983130B00A43A5F /* testautomation_timer.c */; };
		F35E56D72983130F00A43A5F /* testautomation_stdlib.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56BE2983130C00A43A5F /* testautomation_stdlib.c */; };
		F35E56D82983130F00A43A5F /* testautomation_images.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56BF2983130C00A43A5F /* testautomation_images.c */; };
		F35E56D92983130F00A43A5F /* testautomation_pixels.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C02983130C00A43A5F /* testautomation_pixels.c */; };
		F35E56DA2983130F00A43A5F /* testautomation_video.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C12983130C00A43A5F /* testautomation_video.c */; };
		F35E56DB2983130F00A43A5F /* testautomation_platform.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C32983130D00A43A5F /* testautomation_platform.c */; };
		F35E56DC2983130F00A43A5F /* testautomation_audio.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C42983130D00A43A5F /* testautomation_audio.c */; };
		F35E56DD2983130F00A43A5F /* testautomation_rect.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C52983130D00A43A5F /* testautomation_rect.c */; };
		F35E56DE2983130F00A43A5F /* testautomation_joystick.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C62983130D00A43A5F /* testautomation_joystick.c */; };
		F35E56DF2983130F00A43A5F /* testautomation_keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C72983130E00A43A5F /* testautomation_keyboard.c */; };
		F35E56E02983130F00A43A5F /* testautomation_sdltest.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C82983130E00A43A5F /* testautomation_sdltest.c */; };
		F35E56E12983130F00A43A5F /* testautomation_guid.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56C92983130E00A43A5F /* testautomation_guid.c */; };
		F35E56E32983130F00A43A5F /* testautomation_surface.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56CB2983130F00A43A5F /* testautomation_surface.c */; };
		F35E56E42983130F00A43A5F /* testautomation.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56CC2983130F00A43A5F /* testautomation.c */; };
		F35E56E52983130F00A43A5F /* testautomation_mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = F35E56CD2983130F00A43A5F /* testautomation_mouse.c */; };
		F36C34212C0F85DB00991150 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F36C34232C0F85DB00991150 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F36C342D2C0F869B00991150 /* testcamera.c in Sources */ = {isa = PBXBuildFile; fileRef = F36C342C2C0F869B00991150 /* testcamera.c */; };
		F36C342E2C0F869B00991150 /* testcamera.c in Sources */ = {isa = PBXBuildFile; fileRef = F36C342C2C0F869B00991150 /* testcamera.c */; };
		F399C64E2A78929400C86979 /* gamepadutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F399C6492A78929400C86979 /* gamepadutils.c */; };
		F399C64F2A78929400C86979 /* gamepadutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F399C6492A78929400C86979 /* gamepadutils.c */; };
		F399C6512A7892D800C86979 /* testautomation_intrinsics.c in Sources */ = {isa = PBXBuildFile; fileRef = F399C6502A7892D800C86979 /* testautomation_intrinsics.c */; };
		F399C6522A7892D800C86979 /* testautomation_intrinsics.c in Sources */ = {isa = PBXBuildFile; fileRef = F399C6502A7892D800C86979 /* testautomation_intrinsics.c */; };
		F399C6552A78933100C86979 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F399C6542A78933000C86979 /* Cocoa.framework */; };
		F3B7FD642D73FC630086D1D0 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3B7FD662D73FC630086D1D0 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3B7FD6C2D73FC9E0086D1D0 /* testpen.c in Sources */ = {isa = PBXBuildFile; fileRef = F3B7FD6B2D73FC9E0086D1D0 /* testpen.c */; };
		F3C17C7728E40BC800E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C7928E40C6E00E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C7B28E40D4E00E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C7C28E40D7400E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C7D28E40F9D00E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C7E28E40FDD00E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C7F28E4101000E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C8028E410A400E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C8128E410C900E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C8228E4112900E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C8328E4124400E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C8428E4126400E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17C8528E4127D00E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17CEB28E4177600E1A26D /* testgeometry.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17CD628E416AC00E1A26D /* testgeometry.c */; };
		F3C17CEC28E417EB00E1A26D /* testutils.c in Sources */ = {isa = PBXBuildFile; fileRef = F3C17C7328E40ADE00E1A26D /* testutils.c */; };
		F3C17D3928E424B800E1A26D /* sample.wav in Resources */ = {isa = PBXBuildFile; fileRef = 00794E6209D20839003FC8A1 /* sample.wav */; };
		F3C17D3B28E4252900E1A26D /* icon.bmp in Resources */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		F3C2CAC62C5C8BD6004D7998 /* unifont-15.1.05.hex in Resources */ = {isa = PBXBuildFile; fileRef = F3C2CAC52C5C8BD6004D7998 /* unifont-15.1.05.hex */; };
		F3C2CB072C5D3FB2004D7998 /* icon.bmp in Resources */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		F3CB56892A7895F800766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB568A2A7895F800766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB568C2A7896BF00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB568D2A7896BF00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56902A7896F900766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56912A7896F900766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56932A78971600766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56942A78971600766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56962A78971F00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56972A78971F00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56992A78972700766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB569A2A78972700766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB569C2A78972F00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB569D2A78972F00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB569F2A78973700766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56A02A78973700766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56A22A78974000766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56A32A78974000766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56A52A78974800766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56A62A78974800766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56A82A78975100766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56A92A78975100766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56AB2A78975A00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56AC2A78975A00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56AE2A78976200766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56AF2A78976200766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56B12A78976800766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56B22A78976800766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56B42A78977000766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56B52A78977000766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56B72A78977D00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56B82A78977D00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56BA2A78978700766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56BB2A78978700766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56BD2A78979000766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56BE2A78979000766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56C02A78979600766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56C12A78979600766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56C32A78979C00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56C42A78979C00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56C62A7897A500766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56C72A7897A500766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56C92A7897AE00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56CA2A7897AE00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56CC2A7897B500766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56CD2A7897B500766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56CF2A7897BE00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56D02A7897BE00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56D22A7897C600766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56D32A7897C600766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56D52A7897CD00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56D62A7897CD00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56D92A7897E200766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56DA2A7897E200766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56DC2A7897E900766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56DD2A7897E900766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56DF2A7897F000766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56E02A7897F000766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56E22A7897F800766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56E32A7897F800766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56E52A7897FE00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56E62A7897FE00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56E82A78980600766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56E92A78980600766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56EB2A78980D00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56EC2A78980D00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56EE2A78981500766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56EF2A78981500766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56F12A78981C00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56F22A78981C00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56F42A78982300766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56F52A78982300766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56F72A78982B00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56F82A78982B00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56FA2A78983200766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56FB2A78983200766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB56FD2A78983C00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB56FE2A78983C00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB57002A78984300766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB57012A78984300766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB57032A78984A00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB57042A78984A00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB57062A78985400766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB57072A78985400766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB57092A78985A00766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB570A2A78985A00766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB570C2A78986000766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB570D2A78986000766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3CB570F2A78986700766177 /* SDL3.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; };
		F3CB57102A78986700766177 /* SDL3.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA643093FFD41000C53B3 /* SDL3.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		001799471074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC566AB0761D90300A33029;
			remoteInfo = checkkeys;
		};
		0017994B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC566C50761D90300A33029;
			remoteInfo = loopwave;
		};
		0017994F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017957410741F7900F5D044;
			remoteInfo = testatomic;
		};
		001799511074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179595107421BF00F5D044;
			remoteInfo = testaudioinfo;
		};
		001799591074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179756107431B300F5D044;
			remoteInfo = testdraw;
		};
		0017995D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC566FB0761D90300A33029;
			remoteInfo = testerror;
		};
		0017995F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F340109CA1BFF00EBEB88;
			remoteInfo = testfile;
		};
		001799651074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017970910742F3200F5D044;
			remoteInfo = testgl;
		};
		001799671074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179730107430D600F5D044;
			remoteInfo = testhaptic;
		};
		001799691074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567230761D90400A33029;
			remoteInfo = testthread;
		};
		0017996B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F342009CA1F0300EBEB88;
			remoteInfo = testiconv;
		};
		0017996D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179776107432AE00F5D044;
			remoteInfo = testime;
		};
		0017996F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001797961074334C00F5D044;
			remoteInfo = testintersections;
		};
		001799731074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC5673D0761D90400A33029;
			remoteInfo = testkeys;
		};
		001799751074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001797B8107433C600F5D044;
			remoteInfo = testloadso;
		};
		001799771074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC5674A0761D90400A33029;
			remoteInfo = testlock;
		};
		0017997B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001797FA1074355200F5D044;
			remoteInfo = testmultiaudio;
		};
		0017997F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001798781074392D00F5D044;
			remoteInfo = testnativex11;
		};
		001799831074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F343C09CA1FB300EBEB88;
			remoteInfo = testoverlay;
		};
		001799871074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F345909CA204F00EBEB88;
			remoteInfo = testplatform;
		};
		001799891074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017989D107439DF00F5D044;
			remoteInfo = testpower;
		};
		0017998B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001798DA10743BEC00F5D044;
			remoteInfo = testresample;
		};
		0017998D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567720761D90500A33029;
			remoteInfo = testsem;
		};
		001799911074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001798FE10743F1000F5D044;
			remoteInfo = testsprite;
		};
		001799931074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC5678D0761D90500A33029;
			remoteInfo = testtimer;
		};
		001799951074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567A70761D90500A33029;
			remoteInfo = testversion;
		};
		0017999D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017992010743FB700F5D044;
			remoteInfo = testwm;
		};
		001799A11074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567EA0761D90600A33029;
			remoteInfo = torturethread;
		};
		003FA642093FFD41000C53B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF66C0761BA81005FE872;
			remoteInfo = Framework;
		};
		DB0F490417CA5249008798C5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB0F48D917CA51E5008798C5;
			remoteInfo = testdrawchessboard;
		};
		DB0F490617CA5249008798C5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB0F48EF17CA5212008798C5;
			remoteInfo = testfilesystem;
		};
		DB166D6D16A1CEAA00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BBFC08B7164C6862003E6A99;
			remoteInfo = testcontroller;
		};
		DB166D6F16A1CEAF00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4537749112091504002F0F45;
			remoteInfo = testshape;
		};
		DB166DD816A1D38900A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DC416A1D36A00A1396C;
			remoteInfo = testmessage;
		};
		DB166DF116A1D53700A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DDC16A1D50C00A1396C;
			remoteInfo = testrelative;
		};
		DB166E0816A1D5A400A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DF316A1D57C00A1396C;
			remoteInfo = testrendercopyex;
		};
		DB166E1F16A1D5D000A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E0A16A1D5AD00A1396C;
			remoteInfo = testrendertarget;
		};
		DB166E3A16A1D65A00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E2716A1D64D00A1396C;
			remoteInfo = testrumble;
		};
		DB166E5516A1D6B800A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E3D16A1D69000A1396C;
			remoteInfo = testscale;
		};
		DB166E6B16A1D72000A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E5716A1D6F300A1396C;
			remoteInfo = testshader;
		};
		DB166E9516A1D7CD00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E6D16A1D78400A1396C;
			remoteInfo = testspriteminimal;
		};
		DB166E9716A1D7CF00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E8016A1D78C00A1396C;
			remoteInfo = teststreaming;
		};
		F35E56E62983133F00A43A5F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F35E56A2298312CB00A43A5F;
			remoteInfo = testautomation;
		};
		F3E1F7FE2A78C3AD00AC76D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB89956D18A19ABA0092407C;
			remoteInfo = testhotplug;
		};
		F3E1F8002A78C3BE00AC76D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB445EE618184B7000B306B0;
			remoteInfo = testdropfile;
		};
		F3E1F8022A78C3C500AC76D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F3C17CDB28E416CF00E1A26D;
			remoteInfo = testgeometry;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		00794E6409D2084F003FC8A1 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				00794E6609D20865003FC8A1 /* sample.wav in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00794EEC09D2371F003FC8A1 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				00794EF009D23739003FC8A1 /* utf8.txt in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00794EF409D237C7003FC8A1 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				00794EF709D237DE003FC8A1 /* moose.dat in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DDA16A1D40F00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				DB166DDB16A1D42F00A1396C /* icon.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2116A1D5DF00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				DB166E2316A1D60B00A1396C /* icon.bmp in CopyFiles */,
				DB166E2216A1D5EC00A1396C /* sample.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2416A1D61000A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				DB166E2516A1D61900A1396C /* icon.bmp in CopyFiles */,
				DB166E2616A1D61900A1396C /* sample.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E4C16A1D69000A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				DB166E4D16A1D69000A1396C /* icon.bmp in CopyFiles */,
				DB166E4E16A1D69000A1396C /* sample.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E9916A1D7EE00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				DB166E9A16A1D7F700A1396C /* moose.dat in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E9B16A1D7FC00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				DB166E9C16A1D80900A1396C /* icon.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166ECE16A1D85400A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F36C34222C0F85DB00991150 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F36C34232C0F85DB00991150 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3B7FD652D73FC630086D1D0 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3B7FD662D73FC630086D1D0 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB568B2A7895F800766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB568A2A7895F800766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB568E2A7896BF00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB568D2A7896BF00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56922A7896F900766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56912A7896F900766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56952A78971600766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56942A78971600766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56982A78971F00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56972A78971F00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB569B2A78972700766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB569A2A78972700766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB569E2A78973000766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB569D2A78972F00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56A12A78973700766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56A02A78973700766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56A42A78974000766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56A32A78974000766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56A72A78974800766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56A62A78974800766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56AA2A78975100766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56A92A78975100766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56AD2A78975A00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56AC2A78975A00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56B02A78976200766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56AF2A78976200766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56B32A78976900766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56B22A78976800766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56B62A78977000766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56B52A78977000766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56B92A78977D00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56B82A78977D00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56BC2A78978800766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56BB2A78978700766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56BF2A78979000766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56BE2A78979000766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56C22A78979600766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56C12A78979600766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56C52A78979C00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56C42A78979C00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56C82A7897A500766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56C72A7897A500766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56CB2A7897AE00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56CA2A7897AE00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56CE2A7897B500766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56CD2A7897B500766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56D12A7897BE00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56D02A7897BE00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56D42A7897C600766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56D32A7897C600766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56D72A7897CE00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56D62A7897CD00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56DB2A7897E200766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56DA2A7897E200766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56DE2A7897E900766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56DD2A7897E900766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56E12A7897F000766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56E02A7897F000766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56E42A7897F800766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56E32A7897F800766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56E72A7897FE00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56E62A7897FE00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56EA2A78980600766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56E92A78980600766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56ED2A78980D00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56EC2A78980D00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56F02A78981500766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56EF2A78981500766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56F32A78981C00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56F22A78981C00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56F62A78982400766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56F52A78982300766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56F92A78982B00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56F82A78982B00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56FC2A78983200766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56FB2A78983200766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB56FF2A78983C00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB56FE2A78983C00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB57022A78984300766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB57012A78984300766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB57052A78984A00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB57042A78984A00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB57082A78985400766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB57072A78985400766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB570B2A78985A00766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB570A2A78985A00766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB570E2A78986000766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB570D2A78986000766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3CB57112A78986700766177 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3CB57102A78986700766177 /* SDL3.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0017958C10741F7900F5D044 /* testatomic.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testatomic.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0017958F1074216E00F5D044 /* testatomic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testatomic.c; sourceTree = "<group>"; };
		001795AD107421BF00F5D044 /* testaudioinfo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testaudioinfo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001795B01074222D00F5D044 /* testaudioinfo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testaudioinfo.c; sourceTree = "<group>"; };
		0017972110742F3200F5D044 /* testgl.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testgl.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0017972710742FB900F5D044 /* testgl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testgl.c; sourceTree = "<group>"; };
		00179748107430D600F5D044 /* testhaptic.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testhaptic.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0017974E1074315700F5D044 /* testhaptic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testhaptic.c; sourceTree = "<group>"; };
		0017976E107431B300F5D044 /* testdraw.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testdraw.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001797711074320D00F5D044 /* testdraw.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testdraw.c; sourceTree = "<group>"; };
		0017978E107432AE00F5D044 /* testime.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testime.app; sourceTree = BUILT_PRODUCTS_DIR; };
		00179791107432FA00F5D044 /* testime.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testime.c; sourceTree = "<group>"; };
		001797AE1074334C00F5D044 /* testintersections.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testintersections.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001797B31074339C00F5D044 /* testintersections.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testintersections.c; sourceTree = "<group>"; };
		001797D0107433C600F5D044 /* testloadso.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testloadso.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001797D31074343E00F5D044 /* testloadso.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testloadso.c; sourceTree = "<group>"; };
		001798121074355200F5D044 /* testmultiaudio.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testmultiaudio.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001798151074359B00F5D044 /* testmultiaudio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testmultiaudio.c; sourceTree = "<group>"; };
		0017985A107436ED00F5D044 /* testnative.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testnative.c; sourceTree = "<group>"; };
		0017985B107436ED00F5D044 /* testnative.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = testnative.h; sourceTree = "<group>"; };
		0017985C107436ED00F5D044 /* testnativecocoa.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = testnativecocoa.m; sourceTree = "<group>"; };
		00179872107438D000F5D044 /* testnativex11.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testnativex11.c; sourceTree = "<group>"; };
		001798941074392D00F5D044 /* testnative.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testnative.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001798B5107439DF00F5D044 /* testpower.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testpower.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001798B910743A4900F5D044 /* testpower.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testpower.c; sourceTree = "<group>"; };
		001798F210743BEC00F5D044 /* testresample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testresample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		001798F910743E9200F5D044 /* testresample.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testresample.c; sourceTree = "<group>"; };
		0017991610743F1000F5D044 /* testsprite.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testsprite.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0017991910743F5300F5D044 /* testsprite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testsprite.c; sourceTree = "<group>"; };
		0017993810743FB700F5D044 /* testwm.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testwm.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0017993B10743FEF00F5D044 /* testwm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testwm.c; sourceTree = "<group>"; };
		002F341209CA1BFF00EBEB88 /* testfile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testfile.app; sourceTree = BUILT_PRODUCTS_DIR; };
		002F341709CA1C5B00EBEB88 /* testfile.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testfile.c; sourceTree = "<group>"; };
		002F343109CA1F0300EBEB88 /* testiconv.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testiconv.app; sourceTree = BUILT_PRODUCTS_DIR; };
		002F343609CA1F6F00EBEB88 /* testiconv.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testiconv.c; sourceTree = "<group>"; };
		002F344D09CA1FB300EBEB88 /* testoverlay.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testoverlay.app; sourceTree = BUILT_PRODUCTS_DIR; };
		002F345209CA201C00EBEB88 /* testoverlay.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testoverlay.c; sourceTree = "<group>"; };
		002F346A09CA204F00EBEB88 /* testplatform.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testplatform.app; sourceTree = BUILT_PRODUCTS_DIR; };
		002F346F09CA20A600EBEB88 /* testplatform.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testplatform.c; sourceTree = "<group>"; };
		003FA63A093FFD41000C53B3 /* SDL.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SDL.xcodeproj; path = ../SDL/SDL.xcodeproj; sourceTree = SOURCE_ROOT; };
		00794E5D09D20839003FC8A1 /* icon.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = icon.bmp; sourceTree = "<group>"; };
		00794E5E09D20839003FC8A1 /* moose.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = moose.dat; sourceTree = "<group>"; };
		00794E5F09D20839003FC8A1 /* picture.xbm */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; path = picture.xbm; sourceTree = "<group>"; };
		00794E6109D20839003FC8A1 /* sample.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = sample.bmp; sourceTree = "<group>"; };
		00794E6209D20839003FC8A1 /* sample.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = sample.wav; sourceTree = "<group>"; };
		00794E6309D20839003FC8A1 /* utf8.txt */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; path = utf8.txt; sourceTree = "<group>"; };
		083E4872006D84C97F000001 /* loopwave.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = loopwave.c; sourceTree = "<group>"; };
		083E4878006D85357F000001 /* testerror.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testerror.c; sourceTree = "<group>"; };
		083E487E006D86A17F000001 /* testsem.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testsem.c; sourceTree = "<group>"; };
		083E4880006D86A17F000001 /* testtimer.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testtimer.c; sourceTree = "<group>"; };
		083E4882006D86A17F000001 /* testver.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testver.c; sourceTree = "<group>"; };
		083E4887006D86A17F000001 /* torturethread.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = torturethread.c; sourceTree = "<group>"; };
		092D6D10FFB30A2C7F000001 /* checkkeys.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = checkkeys.c; sourceTree = "<group>"; };
		092D6D58FFB311A97F000001 /* testthread.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testthread.c; sourceTree = "<group>"; };
		092D6D6CFFB313437F000001 /* testkeys.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testkeys.c; sourceTree = "<group>"; };
		092D6D75FFB313BB7F000001 /* testlock.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testlock.c; sourceTree = "<group>"; };
		4537749212091504002F0F45 /* testshape.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testshape.app; sourceTree = BUILT_PRODUCTS_DIR; };
		453774A4120915E3002F0F45 /* testshape.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testshape.c; sourceTree = "<group>"; };
		66E88E8A203B778F0004D44E /* testyuv_cvt.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testyuv_cvt.c; sourceTree = "<group>"; };
		A1A859482BC72FC20045DD6C /* testautomation_properties.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_properties.c; sourceTree = "<group>"; };
		A1A859492BC72FC20045DD6C /* testautomation_subsystems.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_subsystems.c; sourceTree = "<group>"; };
		A1A8594A2BC72FC20045DD6C /* testautomation_log.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_log.c; sourceTree = "<group>"; };
		A1A8594B2BC72FC20045DD6C /* testautomation_time.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_time.c; sourceTree = "<group>"; };
		AAF02FF41F90089800B9A9FB /* SDL_test_memory.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_memory.c; sourceTree = "<group>"; };
		BBFC088E164C6820003E6A99 /* testcontroller.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testcontroller.c; sourceTree = "<group>"; };
		BBFC08CD164C6862003E6A99 /* testcontroller.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testcontroller.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC566B60761D90300A33029 /* checkkeys.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = checkkeys.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC566D10761D90300A33029 /* loopwave.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = loopwave.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567060761D90400A33029 /* testerror.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testerror.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC5672E0761D90400A33029 /* testthread.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testthread.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567480761D90400A33029 /* testkeys.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testkeys.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567550761D90400A33029 /* testlock.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testlock.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC5677D0761D90500A33029 /* testsem.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testsem.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567980761D90500A33029 /* testtimer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testtimer.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567B20761D90500A33029 /* testversion.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testversion.app; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567F50761D90600A33029 /* torturethread.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = torturethread.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB0F48D717CA51D2008798C5 /* testdrawchessboard.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testdrawchessboard.c; sourceTree = "<group>"; };
		DB0F48D817CA51D2008798C5 /* testfilesystem.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testfilesystem.c; sourceTree = "<group>"; };
		DB0F48EC17CA51E5008798C5 /* testdrawchessboard.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testdrawchessboard.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB0F490117CA5212008798C5 /* testfilesystem.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testfilesystem.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166CBC16A1C74100A1396C /* testgles.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testgles.c; sourceTree = "<group>"; };
		DB166CBD16A1C74100A1396C /* testmessage.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testmessage.c; sourceTree = "<group>"; };
		DB166CBF16A1C74100A1396C /* testrelative.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrelative.c; sourceTree = "<group>"; };
		DB166CC016A1C74100A1396C /* testrendercopyex.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrendercopyex.c; sourceTree = "<group>"; };
		DB166CC116A1C74100A1396C /* testrendertarget.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrendertarget.c; sourceTree = "<group>"; };
		DB166CC216A1C74100A1396C /* testrumble.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrumble.c; sourceTree = "<group>"; };
		DB166CC316A1C74100A1396C /* testscale.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testscale.c; sourceTree = "<group>"; };
		DB166CC416A1C74100A1396C /* testshader.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testshader.c; sourceTree = "<group>"; };
		DB166CC516A1C74100A1396C /* testspriteminimal.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testspriteminimal.c; sourceTree = "<group>"; };
		DB166CC616A1C74100A1396C /* teststreaming.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = teststreaming.c; sourceTree = "<group>"; };
		DB166D7F16A1D12400A1396C /* libSDL3_test.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libSDL3_test.a; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166D8416A1D1A500A1396C /* SDL_test_assert.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_assert.c; sourceTree = "<group>"; };
		DB166D8516A1D1A500A1396C /* SDL_test_common.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_common.c; sourceTree = "<group>"; };
		DB166D8616A1D1A500A1396C /* SDL_test_compare.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_compare.c; sourceTree = "<group>"; };
		DB166D8716A1D1A500A1396C /* SDL_test_crc32.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_crc32.c; sourceTree = "<group>"; };
		DB166D8816A1D1A500A1396C /* SDL_test_font.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_font.c; sourceTree = "<group>"; };
		DB166D8916A1D1A500A1396C /* SDL_test_fuzzer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_fuzzer.c; sourceTree = "<group>"; };
		DB166D8A16A1D1A500A1396C /* SDL_test_harness.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_harness.c; sourceTree = "<group>"; };
		DB166D9016A1D1A500A1396C /* SDL_test_log.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_log.c; sourceTree = "<group>"; };
		DB166D9116A1D1A500A1396C /* SDL_test_md5.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_md5.c; sourceTree = "<group>"; };
		DB166DD516A1D36A00A1396C /* testmessage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testmessage.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166DEE16A1D50C00A1396C /* testrelative.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testrelative.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E0516A1D57C00A1396C /* testrendercopyex.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testrendercopyex.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E1C16A1D5AD00A1396C /* testrendertarget.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testrendertarget.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E3816A1D64D00A1396C /* testrumble.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testrumble.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E5216A1D69000A1396C /* testscale.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testscale.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E6816A1D6F300A1396C /* testshader.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testshader.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E7E16A1D78400A1396C /* testspriteminimal.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testspriteminimal.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E9116A1D78C00A1396C /* teststreaming.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = teststreaming.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB445EF818184B7000B306B0 /* testdropfile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testdropfile.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB445EFA18184BB600B306B0 /* testdropfile.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testdropfile.c; sourceTree = "<group>"; };
		DB89957E18A19ABA0092407C /* testhotplug.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testhotplug.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB89958318A19B130092407C /* testhotplug.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testhotplug.c; sourceTree = "<group>"; };
		F35E56AA298312CB00A43A5F /* testautomation.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testautomation.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F35E56B62983130A00A43A5F /* testautomation_main.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_main.c; sourceTree = "<group>"; };
		F35E56B72983130A00A43A5F /* testautomation_hints.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_hints.c; sourceTree = "<group>"; };
		F35E56B82983130A00A43A5F /* testautomation_render.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_render.c; sourceTree = "<group>"; };
		F35E56B92983130B00A43A5F /* testautomation_iostream.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_iostream.c; sourceTree = "<group>"; };
		F35E56BA2983130B00A43A5F /* testautomation_math.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_math.c; sourceTree = "<group>"; };
		F35E56BB2983130B00A43A5F /* testautomation_events.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_events.c; sourceTree = "<group>"; };
		F35E56BC2983130B00A43A5F /* testautomation_clipboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_clipboard.c; sourceTree = "<group>"; };
		F35E56BD2983130B00A43A5F /* testautomation_timer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_timer.c; sourceTree = "<group>"; };
		F35E56BE2983130C00A43A5F /* testautomation_stdlib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_stdlib.c; sourceTree = "<group>"; };
		F35E56BF2983130C00A43A5F /* testautomation_images.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_images.c; sourceTree = "<group>"; };
		F35E56C02983130C00A43A5F /* testautomation_pixels.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_pixels.c; sourceTree = "<group>"; };
		F35E56C12983130C00A43A5F /* testautomation_video.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_video.c; sourceTree = "<group>"; };
		F35E56C32983130D00A43A5F /* testautomation_platform.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_platform.c; sourceTree = "<group>"; };
		F35E56C42983130D00A43A5F /* testautomation_audio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_audio.c; sourceTree = "<group>"; };
		F35E56C52983130D00A43A5F /* testautomation_rect.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_rect.c; sourceTree = "<group>"; };
		F35E56C62983130D00A43A5F /* testautomation_joystick.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_joystick.c; sourceTree = "<group>"; };
		F35E56C72983130E00A43A5F /* testautomation_keyboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_keyboard.c; sourceTree = "<group>"; };
		F35E56C82983130E00A43A5F /* testautomation_sdltest.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_sdltest.c; sourceTree = "<group>"; };
		F35E56C92983130E00A43A5F /* testautomation_guid.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_guid.c; sourceTree = "<group>"; };
		F35E56CB2983130F00A43A5F /* testautomation_surface.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_surface.c; sourceTree = "<group>"; };
		F35E56CC2983130F00A43A5F /* testautomation.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation.c; sourceTree = "<group>"; };
		F35E56CD2983130F00A43A5F /* testautomation_mouse.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_mouse.c; sourceTree = "<group>"; };
		F36C34272C0F85DB00991150 /* testcamera.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testcamera.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F36C342C2C0F869B00991150 /* testcamera.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testcamera.c; sourceTree = "<group>"; };
		F399C6492A78929400C86979 /* gamepadutils.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = gamepadutils.c; sourceTree = "<group>"; };
		F399C6502A7892D800C86979 /* testautomation_intrinsics.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testautomation_intrinsics.c; sourceTree = "<group>"; };
		F399C6542A78933000C86979 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		F3B7FD6A2D73FC630086D1D0 /* testpen.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testpen.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3B7FD6B2D73FC9E0086D1D0 /* testpen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testpen.c; sourceTree = "<group>"; };
		F3C17C6A28E3FD4400E1A26D /* config.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = config.xcconfig; sourceTree = "<group>"; };
		F3C17C7328E40ADE00E1A26D /* testutils.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testutils.c; sourceTree = "<group>"; };
		F3C17CD628E416AC00E1A26D /* testgeometry.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testgeometry.c; sourceTree = "<group>"; };
		F3C17CDC28E416CF00E1A26D /* testgeometry.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testgeometry.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3C2CAC52C5C8BD6004D7998 /* unifont-15.1.05.hex */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "unifont-15.1.05.hex"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0017957A10741F7900F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56932A78971600766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017959B107421BF00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56962A78971F00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017970F10742F3200F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56B42A78977000766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00179736107430D600F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56B72A78977D00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017975C107431B300F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB569F2A78973700766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017977C107432AE00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56C02A78979600766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017979C1074334C00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56C32A78979C00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001797BE107433C600F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56C92A7897AE00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798001074355200F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56D22A7897C600766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798821074392D00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56D52A7897CD00766177 /* SDL3.framework in Frameworks */,
				F399C6552A78933100C86979 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798A3107439DF00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56DF2A7897F000766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798E010743BEC00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56EB2A78980D00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017990410743F1000F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56892A7895F800766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017992610743FB700F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB570C2A78986000766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F340809CA1BFF00EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56AB2A78975A00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F342709CA1F0300EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56BD2A78979000766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F344309CA1FB300EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56D92A7897E200766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F346009CA204F00EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56DC2A7897E900766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4537749012091504002F0F45 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56FA2A78983200766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BBFC08BE164C6862003E6A99 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB569C2A78972F00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566B20761D90300A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB568C2A7896BF00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566CC0761D90300A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56902A7896F900766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567020761D90300A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56A82A78975100766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC5672A0761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB57032A78984A00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567440761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56C62A7897A500766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567510761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56CC2A7897B500766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567790761D90500A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56F42A78982300766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567940761D90500A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB57062A78985400766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567AE0761D90500A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB57092A78985A00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567F10761D90600A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB570F2A78986700766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48DC17CA51E5008798C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56A22A78974000766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48F217CA5212008798C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56AE2A78976200766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166D7C16A1D12400A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DC716A1D36A00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56CF2A7897BE00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DDF16A1D50C00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56E22A7897F800766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DF616A1D57C00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56E52A7897FE00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E0D16A1D5AD00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56E82A78980600766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2A16A1D64D00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56EE2A78981500766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E4016A1D69000A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56F12A78981C00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E5A16A1D6F300A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56F72A78982B00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E7016A1D78400A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56FD2A78983C00766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E8316A1D78C00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB57002A78984300766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB445EE918184B7000B306B0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56A52A78974800766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB89957018A19ABA0092407C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56BA2A78978700766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F35E56A5298312CB00A43A5F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56992A78972700766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F36C34202C0F85DB00991150 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F36C34212C0F85DB00991150 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3B7FD632D73FC630086D1D0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3B7FD642D73FC630086D1D0 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3C17CD928E416CF00E1A26D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3CB56B12A78976800766177 /* SDL3.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		003FA63B093FFD41000C53B3 /* Products */ = {
			isa = PBXGroup;
			children = (
				003FA643093FFD41000C53B3 /* SDL3.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00794E4609D207B4003FC8A1 /* Resources */ = {
			isa = PBXGroup;
			children = (
				00794E5D09D20839003FC8A1 /* icon.bmp */,
				00794E5E09D20839003FC8A1 /* moose.dat */,
				00794E5F09D20839003FC8A1 /* picture.xbm */,
				00794E6109D20839003FC8A1 /* sample.bmp */,
				00794E6209D20839003FC8A1 /* sample.wav */,
				F3C2CAC52C5C8BD6004D7998 /* unifont-15.1.05.hex */,
				00794E6309D20839003FC8A1 /* utf8.txt */,
			);
			name = Resources;
			path = ../../test;
			sourceTree = "<group>";
		};
		08FB7794FE84155DC02AAC07 /* SDLTest */ = {
			isa = PBXGroup;
			children = (
				F3C17C6A28E3FD4400E1A26D /* config.xcconfig */,
				003FA63A093FFD41000C53B3 /* SDL.xcodeproj */,
				08FB7795FE84155DC02AAC07 /* Source */,
				DB166D8316A1D17E00A1396C /* SDL_Test */,
				00794E4609D207B4003FC8A1 /* Resources */,
				1AB674ADFE9D54B511CA2CBB /* Products */,
				F399C6532A78933000C86979 /* Frameworks */,
			);
			comments = "I made these tests link against our \"default\" framework which includes X11 stuff. If you didn't install the X11 headers with Xcode, you might have problems building the SDL.framework (which is a dependency). You can swap the dependencies around to get around this, or you can modify the default SDL.framework target to not include X11 stuff. (Go into its target build options and remove all the Preprocessor macros.)\n\n\n\nWe are sort of in a half-way state at the moment. Going \"all-the-way\" means we copy the SDL.framework inside the app bundle so we can run the test without the step of the user \"installing\" the framework. But there is an oversight/bug in Xcode that doesn't correctly find the location of the framework when in an embedded/nested Xcode project. We could probably try to hack this with a shell script that checks multiple directories for existence, but this is messier and more work than I prefer, so I rather just wait for Apple to fix this. In the meantime...\n\nThe \"All\" target will build the SDL framework from the Xcode project. The other targets do not have this dependency set (for flexibility reasons in case we make changes). If you have not built the framework, you will probably be unable to link. You will either need to build the framework, or you need to add \"-framework SDL\" to the link options and make sure you have the SDL.framework installed somewhere where it can be seen (like /Library/Frameworks...I think we already set this one up.) \n\nTo run though, you should have a copy of the SDL.framework in /Library/Frameworks or ~/Library/Frameworks.\n\n\n\n\ntestgl and testdyngl need -DHAVE_OPENGL\ntestgl needs to link against OpenGL.framework\n\n";
			name = SDLTest;
			sourceTree = "<group>";
		};
		08FB7795FE84155DC02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				092D6D10FFB30A2C7F000001 /* checkkeys.c */,
				F399C6492A78929400C86979 /* gamepadutils.c */,
				083E4872006D84C97F000001 /* loopwave.c */,
				0017958F1074216E00F5D044 /* testatomic.c */,
				001795B01074222D00F5D044 /* testaudioinfo.c */,
				F35E56CC2983130F00A43A5F /* testautomation.c */,
				F35E56C42983130D00A43A5F /* testautomation_audio.c */,
				F35E56BC2983130B00A43A5F /* testautomation_clipboard.c */,
				F35E56BB2983130B00A43A5F /* testautomation_events.c */,
				F35E56C92983130E00A43A5F /* testautomation_guid.c */,
				F35E56B72983130A00A43A5F /* testautomation_hints.c */,
				F35E56BF2983130C00A43A5F /* testautomation_images.c */,
				F399C6502A7892D800C86979 /* testautomation_intrinsics.c */,
				F35E56B92983130B00A43A5F /* testautomation_iostream.c */,
				F35E56C62983130D00A43A5F /* testautomation_joystick.c */,
				F35E56C72983130E00A43A5F /* testautomation_keyboard.c */,
				A1A8594A2BC72FC20045DD6C /* testautomation_log.c */,
				F35E56B62983130A00A43A5F /* testautomation_main.c */,
				F35E56BA2983130B00A43A5F /* testautomation_math.c */,
				F35E56CD2983130F00A43A5F /* testautomation_mouse.c */,
				F35E56C02983130C00A43A5F /* testautomation_pixels.c */,
				F35E56C32983130D00A43A5F /* testautomation_platform.c */,
				A1A859482BC72FC20045DD6C /* testautomation_properties.c */,
				F35E56C52983130D00A43A5F /* testautomation_rect.c */,
				F35E56B82983130A00A43A5F /* testautomation_render.c */,
				F35E56C82983130E00A43A5F /* testautomation_sdltest.c */,
				F35E56BE2983130C00A43A5F /* testautomation_stdlib.c */,
				A1A859492BC72FC20045DD6C /* testautomation_subsystems.c */,
				F35E56CB2983130F00A43A5F /* testautomation_surface.c */,
				A1A8594B2BC72FC20045DD6C /* testautomation_time.c */,
				F35E56BD2983130B00A43A5F /* testautomation_timer.c */,
				F35E56C12983130C00A43A5F /* testautomation_video.c */,
				F36C342C2C0F869B00991150 /* testcamera.c */,
				BBFC088E164C6820003E6A99 /* testcontroller.c */,
				001797711074320D00F5D044 /* testdraw.c */,
				DB0F48D717CA51D2008798C5 /* testdrawchessboard.c */,
				DB445EFA18184BB600B306B0 /* testdropfile.c */,
				083E4878006D85357F000001 /* testerror.c */,
				002F341709CA1C5B00EBEB88 /* testfile.c */,
				DB0F48D817CA51D2008798C5 /* testfilesystem.c */,
				F3C17CD628E416AC00E1A26D /* testgeometry.c */,
				0017972710742FB900F5D044 /* testgl.c */,
				DB166CBC16A1C74100A1396C /* testgles.c */,
				0017974E1074315700F5D044 /* testhaptic.c */,
				DB89958318A19B130092407C /* testhotplug.c */,
				002F343609CA1F6F00EBEB88 /* testiconv.c */,
				00179791107432FA00F5D044 /* testime.c */,
				001797B31074339C00F5D044 /* testintersections.c */,
				092D6D6CFFB313437F000001 /* testkeys.c */,
				001797D31074343E00F5D044 /* testloadso.c */,
				092D6D75FFB313BB7F000001 /* testlock.c */,
				DB166CBD16A1C74100A1396C /* testmessage.c */,
				001798151074359B00F5D044 /* testmultiaudio.c */,
				0017985B107436ED00F5D044 /* testnative.h */,
				0017985A107436ED00F5D044 /* testnative.c */,
				0017985C107436ED00F5D044 /* testnativecocoa.m */,
				00179872107438D000F5D044 /* testnativex11.c */,
				002F345209CA201C00EBEB88 /* testoverlay.c */,
				F3B7FD6B2D73FC9E0086D1D0 /* testpen.c */,
				002F346F09CA20A600EBEB88 /* testplatform.c */,
				001798B910743A4900F5D044 /* testpower.c */,
				DB166CBF16A1C74100A1396C /* testrelative.c */,
				DB166CC016A1C74100A1396C /* testrendercopyex.c */,
				DB166CC116A1C74100A1396C /* testrendertarget.c */,
				001798F910743E9200F5D044 /* testresample.c */,
				DB166CC216A1C74100A1396C /* testrumble.c */,
				DB166CC316A1C74100A1396C /* testscale.c */,
				083E487E006D86A17F000001 /* testsem.c */,
				DB166CC416A1C74100A1396C /* testshader.c */,
				453774A4120915E3002F0F45 /* testshape.c */,
				0017991910743F5300F5D044 /* testsprite.c */,
				DB166CC516A1C74100A1396C /* testspriteminimal.c */,
				DB166CC616A1C74100A1396C /* teststreaming.c */,
				092D6D58FFB311A97F000001 /* testthread.c */,
				083E4880006D86A17F000001 /* testtimer.c */,
				F3C17C7328E40ADE00E1A26D /* testutils.c */,
				083E4882006D86A17F000001 /* testver.c */,
				0017993B10743FEF00F5D044 /* testwm.c */,
				66E88E8A203B778F0004D44E /* testyuv_cvt.c */,
				083E4887006D86A17F000001 /* torturethread.c */,
			);
			name = Source;
			path = ../../test;
			sourceTree = "<group>";
		};
		1AB674ADFE9D54B511CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				BEC566B60761D90300A33029 /* checkkeys.app */,
				BEC566D10761D90300A33029 /* loopwave.app */,
				BEC567060761D90400A33029 /* testerror.app */,
				BEC5672E0761D90400A33029 /* testthread.app */,
				BEC567480761D90400A33029 /* testkeys.app */,
				BEC567550761D90400A33029 /* testlock.app */,
				BEC5677D0761D90500A33029 /* testsem.app */,
				BEC567980761D90500A33029 /* testtimer.app */,
				BEC567B20761D90500A33029 /* testversion.app */,
				BEC567F50761D90600A33029 /* torturethread.app */,
				002F341209CA1BFF00EBEB88 /* testfile.app */,
				002F343109CA1F0300EBEB88 /* testiconv.app */,
				002F344D09CA1FB300EBEB88 /* testoverlay.app */,
				002F346A09CA204F00EBEB88 /* testplatform.app */,
				0017958C10741F7900F5D044 /* testatomic.app */,
				001795AD107421BF00F5D044 /* testaudioinfo.app */,
				0017972110742F3200F5D044 /* testgl.app */,
				00179748107430D600F5D044 /* testhaptic.app */,
				0017976E107431B300F5D044 /* testdraw.app */,
				0017978E107432AE00F5D044 /* testime.app */,
				001797AE1074334C00F5D044 /* testintersections.app */,
				001797D0107433C600F5D044 /* testloadso.app */,
				001798121074355200F5D044 /* testmultiaudio.app */,
				001798941074392D00F5D044 /* testnative.app */,
				001798B5107439DF00F5D044 /* testpower.app */,
				001798F210743BEC00F5D044 /* testresample.app */,
				0017991610743F1000F5D044 /* testsprite.app */,
				0017993810743FB700F5D044 /* testwm.app */,
				4537749212091504002F0F45 /* testshape.app */,
				BBFC08CD164C6862003E6A99 /* testcontroller.app */,
				DB166D7F16A1D12400A1396C /* libSDL3_test.a */,
				DB166DD516A1D36A00A1396C /* testmessage.app */,
				DB166DEE16A1D50C00A1396C /* testrelative.app */,
				DB166E0516A1D57C00A1396C /* testrendercopyex.app */,
				DB166E1C16A1D5AD00A1396C /* testrendertarget.app */,
				DB166E3816A1D64D00A1396C /* testrumble.app */,
				DB166E5216A1D69000A1396C /* testscale.app */,
				DB166E6816A1D6F300A1396C /* testshader.app */,
				DB166E7E16A1D78400A1396C /* testspriteminimal.app */,
				DB166E9116A1D78C00A1396C /* teststreaming.app */,
				DB0F48EC17CA51E5008798C5 /* testdrawchessboard.app */,
				DB0F490117CA5212008798C5 /* testfilesystem.app */,
				DB89957E18A19ABA0092407C /* testhotplug.app */,
				DB445EF818184B7000B306B0 /* testdropfile.app */,
				F3C17CDC28E416CF00E1A26D /* testgeometry.app */,
				F35E56AA298312CB00A43A5F /* testautomation.app */,
				F36C34272C0F85DB00991150 /* testcamera.app */,
				F3B7FD6A2D73FC630086D1D0 /* testpen.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DB166D8316A1D17E00A1396C /* SDL_Test */ = {
			isa = PBXGroup;
			children = (
				DB166D8416A1D1A500A1396C /* SDL_test_assert.c */,
				DB166D8516A1D1A500A1396C /* SDL_test_common.c */,
				DB166D8616A1D1A500A1396C /* SDL_test_compare.c */,
				DB166D8716A1D1A500A1396C /* SDL_test_crc32.c */,
				DB166D8816A1D1A500A1396C /* SDL_test_font.c */,
				DB166D8916A1D1A500A1396C /* SDL_test_fuzzer.c */,
				DB166D8A16A1D1A500A1396C /* SDL_test_harness.c */,
				DB166D9016A1D1A500A1396C /* SDL_test_log.c */,
				DB166D9116A1D1A500A1396C /* SDL_test_md5.c */,
				AAF02FF41F90089800B9A9FB /* SDL_test_memory.c */,
			);
			name = SDL_Test;
			path = ../../src/test;
			sourceTree = "<group>";
		};
		F399C6532A78933000C86979 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F399C6542A78933000C86979 /* Cocoa.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		DB166D7D16A1D12400A1396C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0017957410741F7900F5D044 /* testatomic */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017958610741F7900F5D044 /* Build configuration list for PBXNativeTarget "testatomic" */;
			buildPhases = (
				0017957910741F7900F5D044 /* Sources */,
				0017957A10741F7900F5D044 /* Frameworks */,
				F3CB56952A78971600766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testatomic;
			productName = testalpha;
			productReference = 0017958C10741F7900F5D044 /* testatomic.app */;
			productType = "com.apple.product-type.application";
		};
		00179595107421BF00F5D044 /* testaudioinfo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001795A7107421BF00F5D044 /* Build configuration list for PBXNativeTarget "testaudioinfo" */;
			buildPhases = (
				0017959A107421BF00F5D044 /* Sources */,
				0017959B107421BF00F5D044 /* Frameworks */,
				F3CB56982A78971F00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testaudioinfo;
			productName = testalpha;
			productReference = 001795AD107421BF00F5D044 /* testaudioinfo.app */;
			productType = "com.apple.product-type.application";
		};
		0017970910742F3200F5D044 /* testgl */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017971B10742F3200F5D044 /* Build configuration list for PBXNativeTarget "testgl" */;
			buildPhases = (
				0017970E10742F3200F5D044 /* Sources */,
				0017970F10742F3200F5D044 /* Frameworks */,
				F3CB56B62A78977000766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testgl;
			productName = testalpha;
			productReference = 0017972110742F3200F5D044 /* testgl.app */;
			productType = "com.apple.product-type.application";
		};
		00179730107430D600F5D044 /* testhaptic */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00179742107430D600F5D044 /* Build configuration list for PBXNativeTarget "testhaptic" */;
			buildPhases = (
				00179735107430D600F5D044 /* Sources */,
				00179736107430D600F5D044 /* Frameworks */,
				F3CB56B92A78977D00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testhaptic;
			productName = testalpha;
			productReference = 00179748107430D600F5D044 /* testhaptic.app */;
			productType = "com.apple.product-type.application";
		};
		00179756107431B300F5D044 /* testdraw */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00179768107431B300F5D044 /* Build configuration list for PBXNativeTarget "testdraw" */;
			buildPhases = (
				0017975B107431B300F5D044 /* Sources */,
				0017975C107431B300F5D044 /* Frameworks */,
				F3CB56A12A78973700766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testdraw;
			productName = testalpha;
			productReference = 0017976E107431B300F5D044 /* testdraw.app */;
			productType = "com.apple.product-type.application";
		};
		00179776107432AE00F5D044 /* testime */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00179788107432AE00F5D044 /* Build configuration list for PBXNativeTarget "testime" */;
			buildPhases = (
				0017977B107432AE00F5D044 /* Sources */,
				0017977C107432AE00F5D044 /* Frameworks */,
				F3CB56C22A78979600766177 /* Embed Frameworks */,
				F3C2CAC42C5C8BAF004D7998 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testime;
			productName = testalpha;
			productReference = 0017978E107432AE00F5D044 /* testime.app */;
			productType = "com.apple.product-type.application";
		};
		001797961074334C00F5D044 /* testintersections */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001797A81074334C00F5D044 /* Build configuration list for PBXNativeTarget "testintersections" */;
			buildPhases = (
				0017979B1074334C00F5D044 /* Sources */,
				0017979C1074334C00F5D044 /* Frameworks */,
				F3CB56C52A78979C00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testintersections;
			productName = testalpha;
			productReference = 001797AE1074334C00F5D044 /* testintersections.app */;
			productType = "com.apple.product-type.application";
		};
		001797B8107433C600F5D044 /* testloadso */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001797CA107433C600F5D044 /* Build configuration list for PBXNativeTarget "testloadso" */;
			buildPhases = (
				001797BD107433C600F5D044 /* Sources */,
				001797BE107433C600F5D044 /* Frameworks */,
				F3CB56CB2A7897AE00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testloadso;
			productName = testalpha;
			productReference = 001797D0107433C600F5D044 /* testloadso.app */;
			productType = "com.apple.product-type.application";
		};
		001797FA1074355200F5D044 /* testmultiaudio */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017980C1074355200F5D044 /* Build configuration list for PBXNativeTarget "testmultiaudio" */;
			buildPhases = (
				001797FF1074355200F5D044 /* Sources */,
				001798001074355200F5D044 /* Frameworks */,
				F3C17D3828E424B100E1A26D /* Resources */,
				F3CB56D42A7897C600766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testmultiaudio;
			productName = testalpha;
			productReference = 001798121074355200F5D044 /* testmultiaudio.app */;
			productType = "com.apple.product-type.application";
		};
		001798781074392D00F5D044 /* testnative */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017988E1074392D00F5D044 /* Build configuration list for PBXNativeTarget "testnative" */;
			buildPhases = (
				0017987E1074392D00F5D044 /* Sources */,
				001798821074392D00F5D044 /* Frameworks */,
				DB166DDA16A1D40F00A1396C /* CopyFiles */,
				F3CB56D72A7897CE00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testnative;
			productName = testalpha;
			productReference = 001798941074392D00F5D044 /* testnative.app */;
			productType = "com.apple.product-type.application";
		};
		0017989D107439DF00F5D044 /* testpower */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001798AF107439DF00F5D044 /* Build configuration list for PBXNativeTarget "testpower" */;
			buildPhases = (
				001798A2107439DF00F5D044 /* Sources */,
				001798A3107439DF00F5D044 /* Frameworks */,
				F3CB56E12A7897F000766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testpower;
			productName = testalpha;
			productReference = 001798B5107439DF00F5D044 /* testpower.app */;
			productType = "com.apple.product-type.application";
		};
		001798DA10743BEC00F5D044 /* testresample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001798EC10743BEC00F5D044 /* Build configuration list for PBXNativeTarget "testresample" */;
			buildPhases = (
				001798DF10743BEC00F5D044 /* Sources */,
				001798E010743BEC00F5D044 /* Frameworks */,
				F3CB56ED2A78980D00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testresample;
			productName = testalpha;
			productReference = 001798F210743BEC00F5D044 /* testresample.app */;
			productType = "com.apple.product-type.application";
		};
		001798FE10743F1000F5D044 /* testsprite */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017991010743F1000F5D044 /* Build configuration list for PBXNativeTarget "testsprite" */;
			buildPhases = (
				0017990310743F1000F5D044 /* Sources */,
				0017990410743F1000F5D044 /* Frameworks */,
				F3C17D3A28E4252200E1A26D /* Resources */,
				F3CB568B2A7895F800766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testsprite;
			productName = testalpha;
			productReference = 0017991610743F1000F5D044 /* testsprite.app */;
			productType = "com.apple.product-type.application";
		};
		0017992010743FB700F5D044 /* testwm */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017993210743FB700F5D044 /* Build configuration list for PBXNativeTarget "testwm" */;
			buildPhases = (
				0017992510743FB700F5D044 /* Sources */,
				0017992610743FB700F5D044 /* Frameworks */,
				F3CB570E2A78986000766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testwm;
			productName = testalpha;
			productReference = 0017993810743FB700F5D044 /* testwm.app */;
			productType = "com.apple.product-type.application";
		};
		002F340109CA1BFF00EBEB88 /* testfile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F340E09CA1BFF00EBEB88 /* Build configuration list for PBXNativeTarget "testfile" */;
			buildPhases = (
				002F340709CA1BFF00EBEB88 /* Sources */,
				002F340809CA1BFF00EBEB88 /* Frameworks */,
				F3CB56AD2A78975A00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testfile;
			productName = testalpha;
			productReference = 002F341209CA1BFF00EBEB88 /* testfile.app */;
			productType = "com.apple.product-type.application";
		};
		002F342009CA1F0300EBEB88 /* testiconv */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F342D09CA1F0300EBEB88 /* Build configuration list for PBXNativeTarget "testiconv" */;
			buildPhases = (
				002F342609CA1F0300EBEB88 /* Sources */,
				002F342709CA1F0300EBEB88 /* Frameworks */,
				00794EEC09D2371F003FC8A1 /* CopyFiles */,
				F3CB56BF2A78979000766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testiconv;
			productName = testalpha;
			productReference = 002F343109CA1F0300EBEB88 /* testiconv.app */;
			productType = "com.apple.product-type.application";
		};
		002F343C09CA1FB300EBEB88 /* testoverlay */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F344909CA1FB300EBEB88 /* Build configuration list for PBXNativeTarget "testoverlay" */;
			buildPhases = (
				002F344209CA1FB300EBEB88 /* Sources */,
				002F344309CA1FB300EBEB88 /* Frameworks */,
				00794EF409D237C7003FC8A1 /* CopyFiles */,
				F3CB56DB2A7897E200766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testoverlay;
			productName = testalpha;
			productReference = 002F344D09CA1FB300EBEB88 /* testoverlay.app */;
			productType = "com.apple.product-type.application";
		};
		002F345909CA204F00EBEB88 /* testplatform */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F346609CA204F00EBEB88 /* Build configuration list for PBXNativeTarget "testplatform" */;
			buildPhases = (
				002F345F09CA204F00EBEB88 /* Sources */,
				002F346009CA204F00EBEB88 /* Frameworks */,
				F3CB56DE2A7897E900766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testplatform;
			productName = testalpha;
			productReference = 002F346A09CA204F00EBEB88 /* testplatform.app */;
			productType = "com.apple.product-type.application";
		};
		4537749112091504002F0F45 /* testshape */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4537749A1209150C002F0F45 /* Build configuration list for PBXNativeTarget "testshape" */;
			buildPhases = (
				4537748F12091504002F0F45 /* Sources */,
				4537749012091504002F0F45 /* Frameworks */,
				DB166ECE16A1D85400A1396C /* CopyFiles */,
				F3CB56FC2A78983200766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testshape;
			productName = testshape;
			productReference = 4537749212091504002F0F45 /* testshape.app */;
			productType = "com.apple.product-type.application";
		};
		BBFC08B7164C6862003E6A99 /* testcontroller */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BBFC08CA164C6862003E6A99 /* Build configuration list for PBXNativeTarget "testcontroller" */;
			buildPhases = (
				BBFC08BC164C6862003E6A99 /* Sources */,
				BBFC08BE164C6862003E6A99 /* Frameworks */,
				F3CB569E2A78973000766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testcontroller;
			productName = testjoystick;
			productReference = BBFC08CD164C6862003E6A99 /* testcontroller.app */;
			productType = "com.apple.product-type.application";
		};
		BEC566AB0761D90300A33029 /* checkkeys */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B593808BDB826006539E9 /* Build configuration list for PBXNativeTarget "checkkeys" */;
			buildPhases = (
				BEC566B00761D90300A33029 /* Sources */,
				BEC566B20761D90300A33029 /* Frameworks */,
				F3CB568E2A7896BF00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = checkkeys;
			productName = checkkeys;
			productReference = BEC566B60761D90300A33029 /* checkkeys.app */;
			productType = "com.apple.product-type.application";
		};
		BEC566C50761D90300A33029 /* loopwave */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B594008BDB826006539E9 /* Build configuration list for PBXNativeTarget "loopwave" */;
			buildPhases = (
				BEC566CA0761D90300A33029 /* Sources */,
				BEC566CC0761D90300A33029 /* Frameworks */,
				00794E6409D2084F003FC8A1 /* CopyFiles */,
				F3CB56922A7896F900766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = loopwave;
			productName = loopwave;
			productReference = BEC566D10761D90300A33029 /* loopwave.app */;
			productType = "com.apple.product-type.application";
		};
		BEC566FB0761D90300A33029 /* testerror */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B595008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testerror" */;
			buildPhases = (
				BEC567000761D90300A33029 /* Sources */,
				BEC567020761D90300A33029 /* Frameworks */,
				F3CB56AA2A78975100766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testerror;
			productName = testerror;
			productReference = BEC567060761D90400A33029 /* testerror.app */;
			productType = "com.apple.product-type.application";
		};
		BEC567230761D90400A33029 /* testthread */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B595C08BDB826006539E9 /* Build configuration list for PBXNativeTarget "testthread" */;
			buildPhases = (
				BEC567280761D90400A33029 /* Sources */,
				BEC5672A0761D90400A33029 /* Frameworks */,
				F3CB57052A78984A00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testthread;
			productName = testthread;
			productReference = BEC5672E0761D90400A33029 /* testthread.app */;
			productType = "com.apple.product-type.application";
		};
		BEC5673D0761D90400A33029 /* testkeys */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B596408BDB826006539E9 /* Build configuration list for PBXNativeTarget "testkeys" */;
			buildPhases = (
				BEC567420761D90400A33029 /* Sources */,
				BEC567440761D90400A33029 /* Frameworks */,
				F3CB56C82A7897A500766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testkeys;
			productName = testkeys;
			productReference = BEC567480761D90400A33029 /* testkeys.app */;
			productType = "com.apple.product-type.application";
		};
		BEC5674A0761D90400A33029 /* testlock */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B596808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testlock" */;
			buildPhases = (
				BEC5674F0761D90400A33029 /* Sources */,
				BEC567510761D90400A33029 /* Frameworks */,
				F3CB56CE2A7897B500766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testlock;
			productName = testlock;
			productReference = BEC567550761D90400A33029 /* testlock.app */;
			productType = "com.apple.product-type.application";
		};
		BEC567720761D90500A33029 /* testsem */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B597008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testsem" */;
			buildPhases = (
				BEC567770761D90500A33029 /* Sources */,
				BEC567790761D90500A33029 /* Frameworks */,
				F3CB56F62A78982400766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testsem;
			productName = testsem;
			productReference = BEC5677D0761D90500A33029 /* testsem.app */;
			productType = "com.apple.product-type.application";
		};
		BEC5678D0761D90500A33029 /* testtimer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B597808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testtimer" */;
			buildPhases = (
				BEC567920761D90500A33029 /* Sources */,
				BEC567940761D90500A33029 /* Frameworks */,
				F3CB57082A78985400766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testtimer;
			productName = testtimer;
			productReference = BEC567980761D90500A33029 /* testtimer.app */;
			productType = "com.apple.product-type.application";
		};
		BEC567A70761D90500A33029 /* testversion */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B598008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testversion" */;
			buildPhases = (
				BEC567AC0761D90500A33029 /* Sources */,
				BEC567AE0761D90500A33029 /* Frameworks */,
				F3CB570B2A78985A00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testversion;
			productName = testversion;
			productReference = BEC567B20761D90500A33029 /* testversion.app */;
			productType = "com.apple.product-type.application";
		};
		BEC567EA0761D90600A33029 /* torturethread */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B599408BDB826006539E9 /* Build configuration list for PBXNativeTarget "torturethread" */;
			buildPhases = (
				BEC567EF0761D90600A33029 /* Sources */,
				BEC567F10761D90600A33029 /* Frameworks */,
				F3CB57112A78986700766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = torturethread;
			productName = torturethread;
			productReference = BEC567F50761D90600A33029 /* torturethread.app */;
			productType = "com.apple.product-type.application";
		};
		DB0F48D917CA51E5008798C5 /* testdrawchessboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB0F48E917CA51E5008798C5 /* Build configuration list for PBXNativeTarget "testdrawchessboard" */;
			buildPhases = (
				DB0F48DA17CA51E5008798C5 /* Sources */,
				DB0F48DC17CA51E5008798C5 /* Frameworks */,
				F3CB56A42A78974000766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testdrawchessboard;
			productName = testalpha;
			productReference = DB0F48EC17CA51E5008798C5 /* testdrawchessboard.app */;
			productType = "com.apple.product-type.application";
		};
		DB0F48EF17CA5212008798C5 /* testfilesystem */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB0F48FE17CA5212008798C5 /* Build configuration list for PBXNativeTarget "testfilesystem" */;
			buildPhases = (
				DB0F48F017CA5212008798C5 /* Sources */,
				DB0F48F217CA5212008798C5 /* Frameworks */,
				F3CB56B02A78976200766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testfilesystem;
			productName = testalpha;
			productReference = DB0F490117CA5212008798C5 /* testfilesystem.app */;
			productType = "com.apple.product-type.application";
		};
		DB166D7E16A1D12400A1396C /* SDL3_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166D8016A1D12400A1396C /* Build configuration list for PBXNativeTarget "SDL3_test" */;
			buildPhases = (
				DB166D7B16A1D12400A1396C /* Sources */,
				DB166D7C16A1D12400A1396C /* Frameworks */,
				DB166D7D16A1D12400A1396C /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SDL3_test;
			productName = SDL_test;
			productReference = DB166D7F16A1D12400A1396C /* libSDL3_test.a */;
			productType = "com.apple.product-type.library.static";
		};
		DB166DC416A1D36A00A1396C /* testmessage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166DD216A1D36A00A1396C /* Build configuration list for PBXNativeTarget "testmessage" */;
			buildPhases = (
				DB166DC516A1D36A00A1396C /* Sources */,
				DB166DC716A1D36A00A1396C /* Frameworks */,
				F3CB56D12A7897BE00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testmessage;
			productName = testalpha;
			productReference = DB166DD516A1D36A00A1396C /* testmessage.app */;
			productType = "com.apple.product-type.application";
		};
		DB166DDC16A1D50C00A1396C /* testrelative */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166DEB16A1D50C00A1396C /* Build configuration list for PBXNativeTarget "testrelative" */;
			buildPhases = (
				DB166DDD16A1D50C00A1396C /* Sources */,
				DB166DDF16A1D50C00A1396C /* Frameworks */,
				F3CB56E42A7897F800766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrelative;
			productName = testalpha;
			productReference = DB166DEE16A1D50C00A1396C /* testrelative.app */;
			productType = "com.apple.product-type.application";
		};
		DB166DF316A1D57C00A1396C /* testrendercopyex */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E0216A1D57C00A1396C /* Build configuration list for PBXNativeTarget "testrendercopyex" */;
			buildPhases = (
				DB166DF416A1D57C00A1396C /* Sources */,
				DB166DF616A1D57C00A1396C /* Frameworks */,
				DB166E2116A1D5DF00A1396C /* CopyFiles */,
				F3CB56E72A7897FE00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrendercopyex;
			productName = testalpha;
			productReference = DB166E0516A1D57C00A1396C /* testrendercopyex.app */;
			productType = "com.apple.product-type.application";
		};
		DB166E0A16A1D5AD00A1396C /* testrendertarget */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E1916A1D5AD00A1396C /* Build configuration list for PBXNativeTarget "testrendertarget" */;
			buildPhases = (
				DB166E0B16A1D5AD00A1396C /* Sources */,
				DB166E0D16A1D5AD00A1396C /* Frameworks */,
				DB166E2416A1D61000A1396C /* CopyFiles */,
				F3CB56EA2A78980600766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrendertarget;
			productName = testalpha;
			productReference = DB166E1C16A1D5AD00A1396C /* testrendertarget.app */;
			productType = "com.apple.product-type.application";
		};
		DB166E2716A1D64D00A1396C /* testrumble */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E3516A1D64D00A1396C /* Build configuration list for PBXNativeTarget "testrumble" */;
			buildPhases = (
				DB166E2816A1D64D00A1396C /* Sources */,
				DB166E2A16A1D64D00A1396C /* Frameworks */,
				F3CB56F02A78981500766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrumble;
			productName = testalpha;
			productReference = DB166E3816A1D64D00A1396C /* testrumble.app */;
			productType = "com.apple.product-type.application";
		};
		DB166E3D16A1D69000A1396C /* testscale */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E4F16A1D69000A1396C /* Build configuration list for PBXNativeTarget "testscale" */;
			buildPhases = (
				DB166E3E16A1D69000A1396C /* Sources */,
				DB166E4016A1D69000A1396C /* Frameworks */,
				DB166E4C16A1D69000A1396C /* CopyFiles */,
				F3CB56F32A78981C00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testscale;
			productName = testalpha;
			productReference = DB166E5216A1D69000A1396C /* testscale.app */;
			productType = "com.apple.product-type.application";
		};
		DB166E5716A1D6F300A1396C /* testshader */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E6516A1D6F300A1396C /* Build configuration list for PBXNativeTarget "testshader" */;
			buildPhases = (
				DB166E5816A1D6F300A1396C /* Sources */,
				DB166E5A16A1D6F300A1396C /* Frameworks */,
				F3CB56F92A78982B00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testshader;
			productName = testsem;
			productReference = DB166E6816A1D6F300A1396C /* testshader.app */;
			productType = "com.apple.product-type.application";
		};
		DB166E6D16A1D78400A1396C /* testspriteminimal */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E7B16A1D78400A1396C /* Build configuration list for PBXNativeTarget "testspriteminimal" */;
			buildPhases = (
				DB166E6E16A1D78400A1396C /* Sources */,
				DB166E7016A1D78400A1396C /* Frameworks */,
				DB166E9B16A1D7FC00A1396C /* CopyFiles */,
				F3CB56FF2A78983C00766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testspriteminimal;
			productName = testspriteminimal;
			productReference = DB166E7E16A1D78400A1396C /* testspriteminimal.app */;
			productType = "com.apple.product-type.application";
		};
		DB166E8016A1D78C00A1396C /* teststreaming */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E8E16A1D78C00A1396C /* Build configuration list for PBXNativeTarget "teststreaming" */;
			buildPhases = (
				DB166E8116A1D78C00A1396C /* Sources */,
				DB166E8316A1D78C00A1396C /* Frameworks */,
				DB166E9916A1D7EE00A1396C /* CopyFiles */,
				F3CB57022A78984300766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = teststreaming;
			productName = teststreaming;
			productReference = DB166E9116A1D78C00A1396C /* teststreaming.app */;
			productType = "com.apple.product-type.application";
		};
		DB445EE618184B7000B306B0 /* testdropfile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB445EF518184B7000B306B0 /* Build configuration list for PBXNativeTarget "testdropfile" */;
			buildPhases = (
				DB445EE718184B7000B306B0 /* Sources */,
				DB445EE918184B7000B306B0 /* Frameworks */,
				F3CB56A72A78974800766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testdropfile;
			productName = testdropfile;
			productReference = DB445EF818184B7000B306B0 /* testdropfile.app */;
			productType = "com.apple.product-type.application";
		};
		DB89956D18A19ABA0092407C /* testhotplug */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB89957B18A19ABA0092407C /* Build configuration list for PBXNativeTarget "testhotplug" */;
			buildPhases = (
				DB89956E18A19ABA0092407C /* Sources */,
				DB89957018A19ABA0092407C /* Frameworks */,
				F3CB56BC2A78978800766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testhotplug;
			productName = testalpha;
			productReference = DB89957E18A19ABA0092407C /* testhotplug.app */;
			productType = "com.apple.product-type.application";
		};
		F35E56A2298312CB00A43A5F /* testautomation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F35E56A7298312CB00A43A5F /* Build configuration list for PBXNativeTarget "testautomation" */;
			buildPhases = (
				F35E56A3298312CB00A43A5F /* Sources */,
				F35E56A5298312CB00A43A5F /* Frameworks */,
				F3CB569B2A78972700766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testautomation;
			productName = testalpha;
			productReference = F35E56AA298312CB00A43A5F /* testautomation.app */;
			productType = "com.apple.product-type.application";
		};
		F36C341D2C0F85DB00991150 /* testcamera */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F36C34242C0F85DB00991150 /* Build configuration list for PBXNativeTarget "testcamera" */;
			buildPhases = (
				F36C341E2C0F85DB00991150 /* Sources */,
				F36C34202C0F85DB00991150 /* Frameworks */,
				F36C34222C0F85DB00991150 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testcamera;
			productName = testalpha;
			productReference = F36C34272C0F85DB00991150 /* testcamera.app */;
			productType = "com.apple.product-type.application";
		};
		F3B7FD602D73FC630086D1D0 /* testpen */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3B7FD672D73FC630086D1D0 /* Build configuration list for PBXNativeTarget "testpen" */;
			buildPhases = (
				F3B7FD612D73FC630086D1D0 /* Sources */,
				F3B7FD632D73FC630086D1D0 /* Frameworks */,
				F3B7FD652D73FC630086D1D0 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testpen;
			productName = testalpha;
			productReference = F3B7FD6A2D73FC630086D1D0 /* testpen.app */;
			productType = "com.apple.product-type.application";
		};
		F3C17CDB28E416CF00E1A26D /* testgeometry */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3C17CE828E416D000E1A26D /* Build configuration list for PBXNativeTarget "testgeometry" */;
			buildPhases = (
				F3C17CD828E416CF00E1A26D /* Sources */,
				F3C17CD928E416CF00E1A26D /* Frameworks */,
				F3CB56B32A78976900766177 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testgeometry;
			productName = testgeometry;
			productReference = F3C17CDC28E416CF00E1A26D /* testgeometry.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1400;
				LastUpgradeCheck = 0420;
				TargetAttributes = {
					0017957410741F7900F5D044 = {
						ProvisioningStyle = Automatic;
					};
					00179595107421BF00F5D044 = {
						ProvisioningStyle = Automatic;
					};
					0017970910742F3200F5D044 = {
						ProvisioningStyle = Automatic;
					};
					00179730107430D600F5D044 = {
						ProvisioningStyle = Automatic;
					};
					00179756107431B300F5D044 = {
						ProvisioningStyle = Automatic;
					};
					00179776107432AE00F5D044 = {
						ProvisioningStyle = Automatic;
					};
					001797961074334C00F5D044 = {
						ProvisioningStyle = Automatic;
					};
					001797B8107433C600F5D044 = {
						ProvisioningStyle = Automatic;
					};
					001797FA1074355200F5D044 = {
						ProvisioningStyle = Automatic;
					};
					001798781074392D00F5D044 = {
						ProvisioningStyle = Automatic;
					};
					0017989D107439DF00F5D044 = {
						ProvisioningStyle = Automatic;
					};
					001798DA10743BEC00F5D044 = {
						ProvisioningStyle = Automatic;
					};
					001798FE10743F1000F5D044 = {
						ProvisioningStyle = Automatic;
					};
					0017992010743FB700F5D044 = {
						ProvisioningStyle = Automatic;
					};
					002F340109CA1BFF00EBEB88 = {
						ProvisioningStyle = Automatic;
					};
					002F342009CA1F0300EBEB88 = {
						ProvisioningStyle = Automatic;
					};
					002F343C09CA1FB300EBEB88 = {
						ProvisioningStyle = Automatic;
					};
					002F345909CA204F00EBEB88 = {
						ProvisioningStyle = Automatic;
					};
					4537749112091504002F0F45 = {
						ProvisioningStyle = Automatic;
					};
					BBFC08B7164C6862003E6A99 = {
						ProvisioningStyle = Automatic;
					};
					BEC566AB0761D90300A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC566C50761D90300A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC566FB0761D90300A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC567230761D90400A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC5673D0761D90400A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC5674A0761D90400A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC567720761D90500A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC5678D0761D90500A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC567A70761D90500A33029 = {
						ProvisioningStyle = Automatic;
					};
					BEC567EA0761D90600A33029 = {
						ProvisioningStyle = Automatic;
					};
					DB0F48D917CA51E5008798C5 = {
						ProvisioningStyle = Automatic;
					};
					DB0F48EF17CA5212008798C5 = {
						ProvisioningStyle = Automatic;
					};
					DB166DC416A1D36A00A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166DDC16A1D50C00A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166DF316A1D57C00A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166E0A16A1D5AD00A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166E2716A1D64D00A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166E3D16A1D69000A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166E5716A1D6F300A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166E6D16A1D78400A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB166E8016A1D78C00A1396C = {
						ProvisioningStyle = Automatic;
					};
					DB445EE618184B7000B306B0 = {
						ProvisioningStyle = Automatic;
					};
					DB89956D18A19ABA0092407C = {
						ProvisioningStyle = Automatic;
					};
					F35E56A2298312CB00A43A5F = {
						ProvisioningStyle = Automatic;
					};
					F3C17CDB28E416CF00E1A26D = {
						CreatedOnToolsVersion = 14.0.1;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 001B5A0C08BDB826006539E9 /* Build configuration list for PBXProject "SDLTest" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				en,
				Base,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* SDLTest */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 003FA63B093FFD41000C53B3 /* Products */;
					ProjectRef = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				BEC566920761D90300A33029 /* All */,
				DB166D7E16A1D12400A1396C /* SDL3_test */,
				BEC566AB0761D90300A33029 /* checkkeys */,
				BEC566C50761D90300A33029 /* loopwave */,
				0017957410741F7900F5D044 /* testatomic */,
				00179595107421BF00F5D044 /* testaudioinfo */,
				F35E56A2298312CB00A43A5F /* testautomation */,
				F36C341D2C0F85DB00991150 /* testcamera */,
				BBFC08B7164C6862003E6A99 /* testcontroller */,
				00179756107431B300F5D044 /* testdraw */,
				DB0F48D917CA51E5008798C5 /* testdrawchessboard */,
				DB445EE618184B7000B306B0 /* testdropfile */,
				BEC566FB0761D90300A33029 /* testerror */,
				002F340109CA1BFF00EBEB88 /* testfile */,
				DB0F48EF17CA5212008798C5 /* testfilesystem */,
				F3C17CDB28E416CF00E1A26D /* testgeometry */,
				0017970910742F3200F5D044 /* testgl */,
				00179730107430D600F5D044 /* testhaptic */,
				DB89956D18A19ABA0092407C /* testhotplug */,
				002F342009CA1F0300EBEB88 /* testiconv */,
				00179776107432AE00F5D044 /* testime */,
				001797961074334C00F5D044 /* testintersections */,
				BEC5673D0761D90400A33029 /* testkeys */,
				001797B8107433C600F5D044 /* testloadso */,
				BEC5674A0761D90400A33029 /* testlock */,
				DB166DC416A1D36A00A1396C /* testmessage */,
				001797FA1074355200F5D044 /* testmultiaudio */,
				001798781074392D00F5D044 /* testnative */,
				002F343C09CA1FB300EBEB88 /* testoverlay */,
				002F345909CA204F00EBEB88 /* testplatform */,
				F3B7FD602D73FC630086D1D0 /* testpen */,
				0017989D107439DF00F5D044 /* testpower */,
				DB166DDC16A1D50C00A1396C /* testrelative */,
				DB166DF316A1D57C00A1396C /* testrendercopyex */,
				DB166E0A16A1D5AD00A1396C /* testrendertarget */,
				001798DA10743BEC00F5D044 /* testresample */,
				DB166E2716A1D64D00A1396C /* testrumble */,
				DB166E3D16A1D69000A1396C /* testscale */,
				BEC567720761D90500A33029 /* testsem */,
				DB166E5716A1D6F300A1396C /* testshader */,
				4537749112091504002F0F45 /* testshape */,
				001798FE10743F1000F5D044 /* testsprite */,
				DB166E6D16A1D78400A1396C /* testspriteminimal */,
				DB166E8016A1D78C00A1396C /* teststreaming */,
				BEC567230761D90400A33029 /* testthread */,
				BEC5678D0761D90500A33029 /* testtimer */,
				BEC567A70761D90500A33029 /* testversion */,
				0017992010743FB700F5D044 /* testwm */,
				BEC567EA0761D90600A33029 /* torturethread */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		003FA643093FFD41000C53B3 /* SDL3.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL3.framework;
			remoteRef = 003FA642093FFD41000C53B3 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		F3C17D3828E424B100E1A26D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3C17D3928E424B800E1A26D /* sample.wav in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3C17D3A28E4252200E1A26D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3C17D3B28E4252900E1A26D /* icon.bmp in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3C2CAC42C5C8BAF004D7998 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3C2CB072C5D3FB2004D7998 /* icon.bmp in Resources */,
				F3C2CAC62C5C8BD6004D7998 /* unifont-15.1.05.hex in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0017957910741F7900F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001795901074216E00F5D044 /* testatomic.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017959A107421BF00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001795B11074222D00F5D044 /* testaudioinfo.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017970E10742F3200F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017972810742FB900F5D044 /* testgl.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00179735107430D600F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017974F1074315700F5D044 /* testhaptic.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017975B107431B300F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001797721074320D00F5D044 /* testdraw.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017977B107432AE00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00179792107432FA00F5D044 /* testime.c in Sources */,
				F3C17C7C28E40D7400E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017979B1074334C00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001797B41074339C00F5D044 /* testintersections.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001797BD107433C600F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001797D41074343E00F5D044 /* testloadso.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001797FF1074355200F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001798161074359B00F5D044 /* testmultiaudio.c in Sources */,
				F3C17C7D28E40F9D00E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017987E1074392D00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017987F1074392D00F5D044 /* testnative.c in Sources */,
				001798801074392D00F5D044 /* testnativecocoa.m in Sources */,
				F3C17C7E28E40FDD00E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798A2107439DF00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001798BA10743A4900F5D044 /* testpower.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798DF10743BEC00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001798FA10743E9200F5D044 /* testresample.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017990310743F1000F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017991A10743F5300F5D044 /* testsprite.c in Sources */,
				F3C17C8328E4124400E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017992510743FB700F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017993C10743FEF00F5D044 /* testwm.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F340709CA1BFF00EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F341809CA1C5B00EBEB88 /* testfile.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F342609CA1F0300EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F343709CA1F6F00EBEB88 /* testiconv.c in Sources */,
				F3C17C7B28E40D4E00E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F344209CA1FB300EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F345409CA202000EBEB88 /* testoverlay.c in Sources */,
				66E88E8B203B778F0004D44E /* testyuv_cvt.c in Sources */,
				F3C17C7F28E4101000E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F345F09CA204F00EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F347009CA20A600EBEB88 /* testplatform.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4537748F12091504002F0F45 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				453774A5120915E3002F0F45 /* testshape.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BBFC08BC164C6862003E6A99 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BBFC08D0164C6876003E6A99 /* testcontroller.c in Sources */,
				F3C17C7928E40C6E00E1A26D /* testutils.c in Sources */,
				F399C64E2A78929400C86979 /* gamepadutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566B00761D90300A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC566B10761D90300A33029 /* checkkeys.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566CA0761D90300A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC566CB0761D90300A33029 /* loopwave.c in Sources */,
				F3C17C7728E40BC800E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567000761D90300A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567010761D90300A33029 /* testerror.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567280761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567290761D90400A33029 /* testthread.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567420761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567430761D90400A33029 /* testkeys.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC5674F0761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567500761D90400A33029 /* testlock.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567770761D90500A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567780761D90500A33029 /* testsem.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567920761D90500A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567930761D90500A33029 /* testtimer.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567AC0761D90500A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567AD0761D90500A33029 /* testver.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567EF0761D90600A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567F00761D90600A33029 /* torturethread.c in Sources */,
				F399C64F2A78929400C86979 /* gamepadutils.c in Sources */,
				F36C342E2C0F869B00991150 /* testcamera.c in Sources */,
				F399C6522A7892D800C86979 /* testautomation_intrinsics.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48DA17CA51E5008798C5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB0F48EE17CA51F8008798C5 /* testdrawchessboard.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48F017CA5212008798C5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB0F490317CA5225008798C5 /* testfilesystem.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166D7B16A1D12400A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166D9316A1D1A500A1396C /* SDL_test_assert.c in Sources */,
				DB166D9416A1D1A500A1396C /* SDL_test_common.c in Sources */,
				DB166D9516A1D1A500A1396C /* SDL_test_compare.c in Sources */,
				DB166D9616A1D1A500A1396C /* SDL_test_crc32.c in Sources */,
				DB166D9716A1D1A500A1396C /* SDL_test_font.c in Sources */,
				DB166D9816A1D1A500A1396C /* SDL_test_fuzzer.c in Sources */,
				DB166D9916A1D1A500A1396C /* SDL_test_harness.c in Sources */,
				DB166D9F16A1D1A500A1396C /* SDL_test_log.c in Sources */,
				DB166DA016A1D1A500A1396C /* SDL_test_md5.c in Sources */,
				AAF02FFA1F90092700B9A9FB /* SDL_test_memory.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DC516A1D36A00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166DD716A1D37800A1396C /* testmessage.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DDD16A1D50C00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166DF016A1D52500A1396C /* testrelative.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DF416A1D57C00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E0716A1D59400A1396C /* testrendercopyex.c in Sources */,
				F3C17C8028E410A400E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E0B16A1D5AD00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E1E16A1D5C300A1396C /* testrendertarget.c in Sources */,
				F3C17C8128E410C900E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2816A1D64D00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E3C16A1D66500A1396C /* testrumble.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E3E16A1D69000A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E5416A1D6A300A1396C /* testscale.c in Sources */,
				F3C17C8228E4112900E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E5816A1D6F300A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E6A16A1D70C00A1396C /* testshader.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E6E16A1D78400A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E9316A1D7BC00A1396C /* testspriteminimal.c in Sources */,
				F3C17C8428E4126400E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E8116A1D78C00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E9416A1D7C700A1396C /* teststreaming.c in Sources */,
				F3C17C8528E4127D00E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB445EE718184B7000B306B0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB445EFB18184BB600B306B0 /* testdropfile.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB89956E18A19ABA0092407C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB89958418A19B130092407C /* testhotplug.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F35E56A3298312CB00A43A5F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F35E56D12983130F00A43A5F /* testautomation_render.c in Sources */,
				A1A859502BC72FC20045DD6C /* testautomation_subsystems.c in Sources */,
				F399C6512A7892D800C86979 /* testautomation_intrinsics.c in Sources */,
				F35E56D22983130F00A43A5F /* testautomation_iostream.c in Sources */,
				A1A859522BC72FC20045DD6C /* testautomation_log.c in Sources */,
				F35E56E32983130F00A43A5F /* testautomation_surface.c in Sources */,
				F35E56DB2983130F00A43A5F /* testautomation_platform.c in Sources */,
				F35E56DD2983130F00A43A5F /* testautomation_rect.c in Sources */,
				F35E56D52983130F00A43A5F /* testautomation_clipboard.c in Sources */,
				F35E56E52983130F00A43A5F /* testautomation_mouse.c in Sources */,
				F35E56D72983130F00A43A5F /* testautomation_stdlib.c in Sources */,
				F35E56D92983130F00A43A5F /* testautomation_pixels.c in Sources */,
				F35E56E42983130F00A43A5F /* testautomation.c in Sources */,
				F35E56CF2983130F00A43A5F /* testautomation_main.c in Sources */,
				F35E56DE2983130F00A43A5F /* testautomation_joystick.c in Sources */,
				F35E56D82983130F00A43A5F /* testautomation_images.c in Sources */,
				F35E56DC2983130F00A43A5F /* testautomation_audio.c in Sources */,
				F35E56D32983130F00A43A5F /* testautomation_math.c in Sources */,
				F35E56E02983130F00A43A5F /* testautomation_sdltest.c in Sources */,
				F35E56D42983130F00A43A5F /* testautomation_events.c in Sources */,
				A1A859542BC72FC20045DD6C /* testautomation_time.c in Sources */,
				F35E56E12983130F00A43A5F /* testautomation_guid.c in Sources */,
				F35E56D62983130F00A43A5F /* testautomation_timer.c in Sources */,
				F35E56DA2983130F00A43A5F /* testautomation_video.c in Sources */,
				F35E56D02983130F00A43A5F /* testautomation_hints.c in Sources */,
				A1A8594E2BC72FC20045DD6C /* testautomation_properties.c in Sources */,
				F35E56DF2983130F00A43A5F /* testautomation_keyboard.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F36C341E2C0F85DB00991150 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F36C342D2C0F869B00991150 /* testcamera.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3B7FD612D73FC630086D1D0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3B7FD6C2D73FC9E0086D1D0 /* testpen.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3C17CD828E416CF00E1A26D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3C17CEB28E4177600E1A26D /* testgeometry.c in Sources */,
				F3C17CEC28E417EB00E1A26D /* testutils.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		001799481074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC566AB0761D90300A33029 /* checkkeys */;
			targetProxy = 001799471074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017994C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC566C50761D90300A33029 /* loopwave */;
			targetProxy = 0017994B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799501074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017957410741F7900F5D044 /* testatomic */;
			targetProxy = 0017994F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799521074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179595107421BF00F5D044 /* testaudioinfo */;
			targetProxy = 001799511074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017995A1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179756107431B300F5D044 /* testdraw */;
			targetProxy = 001799591074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017995E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC566FB0761D90300A33029 /* testerror */;
			targetProxy = 0017995D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799601074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F340109CA1BFF00EBEB88 /* testfile */;
			targetProxy = 0017995F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799661074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			platformFilters = (
				macos,
			);
			target = 0017970910742F3200F5D044 /* testgl */;
			targetProxy = 001799651074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799681074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179730107430D600F5D044 /* testhaptic */;
			targetProxy = 001799671074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017996A1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567230761D90400A33029 /* testthread */;
			targetProxy = 001799691074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017996C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F342009CA1F0300EBEB88 /* testiconv */;
			targetProxy = 0017996B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017996E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179776107432AE00F5D044 /* testime */;
			targetProxy = 0017996D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799701074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001797961074334C00F5D044 /* testintersections */;
			targetProxy = 0017996F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799741074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC5673D0761D90400A33029 /* testkeys */;
			targetProxy = 001799731074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799761074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001797B8107433C600F5D044 /* testloadso */;
			targetProxy = 001799751074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799781074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC5674A0761D90400A33029 /* testlock */;
			targetProxy = 001799771074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017997C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001797FA1074355200F5D044 /* testmultiaudio */;
			targetProxy = 0017997B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799801074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			platformFilters = (
				macos,
			);
			target = 001798781074392D00F5D044 /* testnative */;
			targetProxy = 0017997F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799841074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F343C09CA1FB300EBEB88 /* testoverlay */;
			targetProxy = 001799831074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799881074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F345909CA204F00EBEB88 /* testplatform */;
			targetProxy = 001799871074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017998A1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017989D107439DF00F5D044 /* testpower */;
			targetProxy = 001799891074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017998C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001798DA10743BEC00F5D044 /* testresample */;
			targetProxy = 0017998B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017998E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567720761D90500A33029 /* testsem */;
			targetProxy = 0017998D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799921074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001798FE10743F1000F5D044 /* testsprite */;
			targetProxy = 001799911074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799941074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC5678D0761D90500A33029 /* testtimer */;
			targetProxy = 001799931074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799961074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567A70761D90500A33029 /* testversion */;
			targetProxy = 001799951074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017999E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017992010743FB700F5D044 /* testwm */;
			targetProxy = 0017999D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799A21074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567EA0761D90600A33029 /* torturethread */;
			targetProxy = 001799A11074403E00F5D044 /* PBXContainerItemProxy */;
		};
		DB0F490517CA5249008798C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB0F48D917CA51E5008798C5 /* testdrawchessboard */;
			targetProxy = DB0F490417CA5249008798C5 /* PBXContainerItemProxy */;
		};
		DB0F490717CA5249008798C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB0F48EF17CA5212008798C5 /* testfilesystem */;
			targetProxy = DB0F490617CA5249008798C5 /* PBXContainerItemProxy */;
		};
		DB166D6E16A1CEAA00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BBFC08B7164C6862003E6A99 /* testcontroller */;
			targetProxy = DB166D6D16A1CEAA00A1396C /* PBXContainerItemProxy */;
		};
		DB166D7016A1CEAF00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4537749112091504002F0F45 /* testshape */;
			targetProxy = DB166D6F16A1CEAF00A1396C /* PBXContainerItemProxy */;
		};
		DB166DD916A1D38900A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DC416A1D36A00A1396C /* testmessage */;
			targetProxy = DB166DD816A1D38900A1396C /* PBXContainerItemProxy */;
		};
		DB166DF216A1D53700A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DDC16A1D50C00A1396C /* testrelative */;
			targetProxy = DB166DF116A1D53700A1396C /* PBXContainerItemProxy */;
		};
		DB166E0916A1D5A400A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DF316A1D57C00A1396C /* testrendercopyex */;
			targetProxy = DB166E0816A1D5A400A1396C /* PBXContainerItemProxy */;
		};
		DB166E2016A1D5D000A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E0A16A1D5AD00A1396C /* testrendertarget */;
			targetProxy = DB166E1F16A1D5D000A1396C /* PBXContainerItemProxy */;
		};
		DB166E3B16A1D65A00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E2716A1D64D00A1396C /* testrumble */;
			targetProxy = DB166E3A16A1D65A00A1396C /* PBXContainerItemProxy */;
		};
		DB166E5616A1D6B800A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E3D16A1D69000A1396C /* testscale */;
			targetProxy = DB166E5516A1D6B800A1396C /* PBXContainerItemProxy */;
		};
		DB166E6C16A1D72000A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E5716A1D6F300A1396C /* testshader */;
			targetProxy = DB166E6B16A1D72000A1396C /* PBXContainerItemProxy */;
		};
		DB166E9616A1D7CD00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E6D16A1D78400A1396C /* testspriteminimal */;
			targetProxy = DB166E9516A1D7CD00A1396C /* PBXContainerItemProxy */;
		};
		DB166E9816A1D7CF00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E8016A1D78C00A1396C /* teststreaming */;
			targetProxy = DB166E9716A1D7CF00A1396C /* PBXContainerItemProxy */;
		};
		F35E56E72983133F00A43A5F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F35E56A2298312CB00A43A5F /* testautomation */;
			targetProxy = F35E56E62983133F00A43A5F /* PBXContainerItemProxy */;
		};
		F3E1F7FF2A78C3AD00AC76D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB89956D18A19ABA0092407C /* testhotplug */;
			targetProxy = F3E1F7FE2A78C3AD00AC76D3 /* PBXContainerItemProxy */;
		};
		F3E1F8012A78C3BE00AC76D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB445EE618184B7000B306B0 /* testdropfile */;
			targetProxy = F3E1F8002A78C3BE00AC76D3 /* PBXContainerItemProxy */;
		};
		F3E1F8032A78C3C500AC76D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F3C17CDB28E416CF00E1A26D /* testgeometry */;
			targetProxy = F3E1F8022A78C3C500AC76D3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0017958910741F7900F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testatomic;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		0017958A10741F7900F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testatomic;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		001795AA107421BF00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testaudioinfo;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		001795AB107421BF00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testaudioinfo;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		0017971E10742F3200F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_OPENGL;
				PRODUCT_NAME = testgl;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Debug;
		};
		0017971F10742F3200F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_OPENGL;
				PRODUCT_NAME = testgl;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Release;
		};
		00179745107430D600F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testhaptic;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		00179746107430D600F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testhaptic;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		0017976B107431B300F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testdraw;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		0017976C107431B300F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testdraw;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		0017978B107432AE00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testime;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		0017978C107432AE00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testime;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		001797AB1074334C00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testintersections;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		001797AC1074334C00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testintersections;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		001797CD107433C600F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testloadso;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		001797CE107433C600F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testloadso;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		0017980F1074355200F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testmultiaudio;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		001798101074355200F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testmultiaudio;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		001798911074392D00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testnative;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Debug;
		};
		001798921074392D00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testnative;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Release;
		};
		001798B2107439DF00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testpower;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		001798B3107439DF00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testpower;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		001798EF10743BEC00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testresample;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		001798F010743BEC00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testresample;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		0017991310743F1000F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testsprite;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		0017991410743F1000F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testsprite;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		0017993510743FB700F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testwm;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		0017993610743FB700F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testwm;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85B21073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F3C17C6A28E3FD4400E1A26D /* config.xcconfig */;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = ../../include;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "$(CONFIG_FRAMEWORK_LDFLAGS)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.libsdl.$(PRODUCT_NAME)";
				SUPPORTED_PLATFORMS = "macosx iphonesimulator iphoneos appletvsimulator appletvos";
				SUPPORTS_MACCATALYST = NO;
				TARGETED_DEVICE_FAMILY = "1,2,3";
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		002A85B31073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "Build All";
			};
			name = Debug;
		};
		002A85B41073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = checkkeys;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85B61073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = loopwave;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85BC1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testerror;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85BD1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testfile;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85C01073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testiconv;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85C21073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testkeys;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85C31073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testlock;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85C51073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testoverlay;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85C71073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testplatform;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85C81073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testsem;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85CA1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testthread;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85CB1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testtimer;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85CC1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testversion;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85D11073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = torturethread;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		002A85D41073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F3C17C6A28E3FD4400E1A26D /* config.xcconfig */;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEPLOYMENT_POSTPROCESSING = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = ../../include;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = "$(CONFIG_FRAMEWORK_LDFLAGS)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.libsdl.$(PRODUCT_NAME)";
				SUPPORTED_PLATFORMS = "macosx iphonesimulator iphoneos appletvsimulator appletvos";
				SUPPORTS_MACCATALYST = NO;
				TARGETED_DEVICE_FAMILY = "1,2,3";
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Release;
		};
		002A85D51073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "Build All";
			};
			name = Release;
		};
		002A85D61073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = checkkeys;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85D81073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = loopwave;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85DE1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testerror;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85DF1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testfile;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85E21073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testiconv;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85E41073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testkeys;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85E51073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testlock;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85E71073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testoverlay;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85E91073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testplatform;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85EA1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testsem;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85EC1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testthread;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85ED1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testtimer;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85EE1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testversion;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		002A85F31073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = torturethread;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		4537749712091509002F0F45 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testshape;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		4537749812091509002F0F45 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testshape;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		BBFC08CB164C6862003E6A99 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Steam Controller support";
				PRODUCT_NAME = testcontroller;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		BBFC08CC164C6862003E6A99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Steam Controller support";
				PRODUCT_NAME = testcontroller;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB0F48EA17CA51E5008798C5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testdrawchessboard;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB0F48EB17CA51E5008798C5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testdrawchessboard;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB0F48FF17CA5212008798C5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testfilesystem;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB0F490017CA5212008798C5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testfilesystem;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166D8116A1D12400A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				EXECUTABLE_PREFIX = lib;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "watchsimulator watchos macosx iphonesimulator iphoneos driverkit appletvsimulator appletvos";
				SUPPORTS_MACCATALYST = YES;
			};
			name = Debug;
		};
		DB166D8216A1D12400A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				EXECUTABLE_PREFIX = lib;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "watchsimulator watchos macosx iphonesimulator iphoneos driverkit appletvsimulator appletvos";
				SUPPORTS_MACCATALYST = YES;
			};
			name = Release;
		};
		DB166DD316A1D36A00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testmessage;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166DD416A1D36A00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testmessage;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166DEC16A1D50C00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrelative;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166DED16A1D50C00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrelative;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E0316A1D57C00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrendercopyex;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E0416A1D57C00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrendercopyex;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E1A16A1D5AD00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrendertarget;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E1B16A1D5AD00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrendertarget;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E3616A1D64D00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrumble;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E3716A1D64D00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testrumble;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E5016A1D69000A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testscale;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E5116A1D69000A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testscale;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E6616A1D6F300A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testshader;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E6716A1D6F300A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testshader;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E7C16A1D78400A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testspriteminimal;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E7D16A1D78400A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testspriteminimal;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB166E8F16A1D78C00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = teststreaming;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB166E9016A1D78C00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = teststreaming;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB445EF618184B7000B306B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testdropfile;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB445EF718184B7000B306B0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testdropfile;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		DB89957C18A19ABA0092407C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testhotplug;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		DB89957D18A19ABA0092407C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = testhotplug;
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		F35E56A8298312CB00A43A5F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		F35E56A9298312CB00A43A5F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		F36C34252C0F85DB00991150 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_KEY_NSCameraUsageDescription = "Testing camera recording";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		F36C34262C0F85DB00991150 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_KEY_NSCameraUsageDescription = "Testing camera recording";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		F3B7FD682D73FC630086D1D0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		F3B7FD692D73FC630086D1D0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
		F3C17CE928E416D000E1A26D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		F3C17CEA28E416D000E1A26D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0017958610741F7900F5D044 /* Build configuration list for PBXNativeTarget "testatomic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017958910741F7900F5D044 /* Debug */,
				0017958A10741F7900F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001795A7107421BF00F5D044 /* Build configuration list for PBXNativeTarget "testaudioinfo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001795AA107421BF00F5D044 /* Debug */,
				001795AB107421BF00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017971B10742F3200F5D044 /* Build configuration list for PBXNativeTarget "testgl" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017971E10742F3200F5D044 /* Debug */,
				0017971F10742F3200F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		00179742107430D600F5D044 /* Build configuration list for PBXNativeTarget "testhaptic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00179745107430D600F5D044 /* Debug */,
				00179746107430D600F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		00179768107431B300F5D044 /* Build configuration list for PBXNativeTarget "testdraw" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017976B107431B300F5D044 /* Debug */,
				0017976C107431B300F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		00179788107432AE00F5D044 /* Build configuration list for PBXNativeTarget "testime" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017978B107432AE00F5D044 /* Debug */,
				0017978C107432AE00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001797A81074334C00F5D044 /* Build configuration list for PBXNativeTarget "testintersections" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001797AB1074334C00F5D044 /* Debug */,
				001797AC1074334C00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001797CA107433C600F5D044 /* Build configuration list for PBXNativeTarget "testloadso" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001797CD107433C600F5D044 /* Debug */,
				001797CE107433C600F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017980C1074355200F5D044 /* Build configuration list for PBXNativeTarget "testmultiaudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017980F1074355200F5D044 /* Debug */,
				001798101074355200F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017988E1074392D00F5D044 /* Build configuration list for PBXNativeTarget "testnative" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001798911074392D00F5D044 /* Debug */,
				001798921074392D00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001798AF107439DF00F5D044 /* Build configuration list for PBXNativeTarget "testpower" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001798B2107439DF00F5D044 /* Debug */,
				001798B3107439DF00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001798EC10743BEC00F5D044 /* Build configuration list for PBXNativeTarget "testresample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001798EF10743BEC00F5D044 /* Debug */,
				001798F010743BEC00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017991010743F1000F5D044 /* Build configuration list for PBXNativeTarget "testsprite" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017991310743F1000F5D044 /* Debug */,
				0017991410743F1000F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017993210743FB700F5D044 /* Build configuration list for PBXNativeTarget "testwm" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017993510743FB700F5D044 /* Debug */,
				0017993610743FB700F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B593808BDB826006539E9 /* Build configuration list for PBXNativeTarget "checkkeys" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B41073008E007319AE /* Debug */,
				002A85D61073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B594008BDB826006539E9 /* Build configuration list for PBXNativeTarget "loopwave" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B61073008E007319AE /* Debug */,
				002A85D81073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B595008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testerror" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85BC1073008E007319AE /* Debug */,
				002A85DE1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B595C08BDB826006539E9 /* Build configuration list for PBXNativeTarget "testthread" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85CA1073008E007319AE /* Debug */,
				002A85EC1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B596408BDB826006539E9 /* Build configuration list for PBXNativeTarget "testkeys" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C21073008E007319AE /* Debug */,
				002A85E41073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B596808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testlock" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C31073008E007319AE /* Debug */,
				002A85E51073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B597008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testsem" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C81073008E007319AE /* Debug */,
				002A85EA1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B597808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testtimer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85CB1073008E007319AE /* Debug */,
				002A85ED1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B598008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testversion" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85CC1073008E007319AE /* Debug */,
				002A85EE1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B599408BDB826006539E9 /* Build configuration list for PBXNativeTarget "torturethread" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85D11073008E007319AE /* Debug */,
				002A85F31073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B599808BDB826006539E9 /* Build configuration list for PBXAggregateTarget "All" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B31073008E007319AE /* Debug */,
				002A85D51073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B5A0C08BDB826006539E9 /* Build configuration list for PBXProject "SDLTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B21073008E007319AE /* Debug */,
				002A85D41073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F340E09CA1BFF00EBEB88 /* Build configuration list for PBXNativeTarget "testfile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85BD1073008E007319AE /* Debug */,
				002A85DF1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F342D09CA1F0300EBEB88 /* Build configuration list for PBXNativeTarget "testiconv" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C01073008E007319AE /* Debug */,
				002A85E21073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F344909CA1FB300EBEB88 /* Build configuration list for PBXNativeTarget "testoverlay" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C51073008E007319AE /* Debug */,
				002A85E71073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F346609CA204F00EBEB88 /* Build configuration list for PBXNativeTarget "testplatform" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C71073008E007319AE /* Debug */,
				002A85E91073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		4537749A1209150C002F0F45 /* Build configuration list for PBXNativeTarget "testshape" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4537749712091509002F0F45 /* Debug */,
				4537749812091509002F0F45 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		BBFC08CA164C6862003E6A99 /* Build configuration list for PBXNativeTarget "testcontroller" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BBFC08CB164C6862003E6A99 /* Debug */,
				BBFC08CC164C6862003E6A99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB0F48E917CA51E5008798C5 /* Build configuration list for PBXNativeTarget "testdrawchessboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB0F48EA17CA51E5008798C5 /* Debug */,
				DB0F48EB17CA51E5008798C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB0F48FE17CA5212008798C5 /* Build configuration list for PBXNativeTarget "testfilesystem" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB0F48FF17CA5212008798C5 /* Debug */,
				DB0F490017CA5212008798C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166D8016A1D12400A1396C /* Build configuration list for PBXNativeTarget "SDL3_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166D8116A1D12400A1396C /* Debug */,
				DB166D8216A1D12400A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166DD216A1D36A00A1396C /* Build configuration list for PBXNativeTarget "testmessage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166DD316A1D36A00A1396C /* Debug */,
				DB166DD416A1D36A00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166DEB16A1D50C00A1396C /* Build configuration list for PBXNativeTarget "testrelative" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166DEC16A1D50C00A1396C /* Debug */,
				DB166DED16A1D50C00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E0216A1D57C00A1396C /* Build configuration list for PBXNativeTarget "testrendercopyex" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E0316A1D57C00A1396C /* Debug */,
				DB166E0416A1D57C00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E1916A1D5AD00A1396C /* Build configuration list for PBXNativeTarget "testrendertarget" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E1A16A1D5AD00A1396C /* Debug */,
				DB166E1B16A1D5AD00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E3516A1D64D00A1396C /* Build configuration list for PBXNativeTarget "testrumble" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E3616A1D64D00A1396C /* Debug */,
				DB166E3716A1D64D00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E4F16A1D69000A1396C /* Build configuration list for PBXNativeTarget "testscale" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E5016A1D69000A1396C /* Debug */,
				DB166E5116A1D69000A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E6516A1D6F300A1396C /* Build configuration list for PBXNativeTarget "testshader" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E6616A1D6F300A1396C /* Debug */,
				DB166E6716A1D6F300A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E7B16A1D78400A1396C /* Build configuration list for PBXNativeTarget "testspriteminimal" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E7C16A1D78400A1396C /* Debug */,
				DB166E7D16A1D78400A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E8E16A1D78C00A1396C /* Build configuration list for PBXNativeTarget "teststreaming" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E8F16A1D78C00A1396C /* Debug */,
				DB166E9016A1D78C00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB445EF518184B7000B306B0 /* Build configuration list for PBXNativeTarget "testdropfile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB445EF618184B7000B306B0 /* Debug */,
				DB445EF718184B7000B306B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB89957B18A19ABA0092407C /* Build configuration list for PBXNativeTarget "testhotplug" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB89957C18A19ABA0092407C /* Debug */,
				DB89957D18A19ABA0092407C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		F35E56A7298312CB00A43A5F /* Build configuration list for PBXNativeTarget "testautomation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F35E56A8298312CB00A43A5F /* Debug */,
				F35E56A9298312CB00A43A5F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		F36C34242C0F85DB00991150 /* Build configuration list for PBXNativeTarget "testcamera" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F36C34252C0F85DB00991150 /* Debug */,
				F36C34262C0F85DB00991150 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		F3B7FD672D73FC630086D1D0 /* Build configuration list for PBXNativeTarget "testpen" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3B7FD682D73FC630086D1D0 /* Debug */,
				F3B7FD692D73FC630086D1D0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		F3C17CE828E416D000E1A26D /* Build configuration list for PBXNativeTarget "testgeometry" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3C17CE928E416D000E1A26D /* Debug */,
				F3C17CEA28E416D000E1A26D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
