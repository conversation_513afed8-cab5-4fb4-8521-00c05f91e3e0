/*
 * SDL3 Pong Game
 * Features: Left paddle, ball, and 3 walls (top, right, bottom)
 * Uses SDL3 callback main style similar to clear.c
 *
 * This code is public domain. Feel free to use it for any purpose!
 */

#define SDL_MAIN_USE_CALLBACKS 1  /* use the callbacks instead of main() */
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <stdbool.h>

/* Window dimensions */
#define WINDOW_WIDTH 800
#define WINDOW_HEIGHT 600

/* Game object dimensions */
#define PADDLE_WIDTH 20
#define PADDLE_HEIGHT 100
#define BALL_SIZE 15
#define WALL_THICKNESS 10

/* Game speeds */
#define PADDLE_SPEED 400.0f  /* pixels per second */
#define BALL_SPEED 300.0f    /* pixels per second */

/* Game state structure */
typedef struct {
    /* Paddle */
    float paddle_y;
    float prev_paddle_y;    /* Previous paddle position for swept collision */
    float target_paddle_y;  /* Target position for smooth mouse/touch movement */

    /* Ball */
    float ball_x, ball_y;
    float ball_vel_x, ball_vel_y;

    /* Input state */
    bool up_pressed;
    bool down_pressed;
    bool mouse_control;     /* Whether mouse/touch is controlling paddle */
    float mouse_y;          /* Current mouse/touch Y position */

    /* Timing */
    Uint64 last_time;
} GameState;

/* We will use this renderer to draw into this window every frame. */
static SDL_Window *window = NULL;
static SDL_Renderer *renderer = NULL;
static GameState game_state = {0};

/* Helper function for swept collision detection with paddle */
static bool check_swept_paddle_collision(float ball_x, float ball_y, float prev_paddle_y, float curr_paddle_y)
{
    /* Check if ball is in the horizontal range for paddle collision */
    if (ball_x > PADDLE_WIDTH + 10 || ball_x < 0) {
        return false;
    }

    /* Check if ball intersects with the paddle's swept area */
    float paddle_top = SDL_min(prev_paddle_y, curr_paddle_y);
    float paddle_bottom = SDL_max(prev_paddle_y, curr_paddle_y) + PADDLE_HEIGHT;

    /* Check if ball overlaps with the swept paddle area */
    return (ball_y + BALL_SIZE >= paddle_top && ball_y <= paddle_bottom);
}

/* Initialize game state */
static void init_game_state(void)
{
    /* Initialize paddle in the middle left */
    game_state.paddle_y = (WINDOW_HEIGHT - PADDLE_HEIGHT) / 2.0f;
    game_state.prev_paddle_y = game_state.paddle_y;
    game_state.target_paddle_y = game_state.paddle_y;

    /* Initialize ball in the center */
    game_state.ball_x = WINDOW_WIDTH / 2.0f;
    game_state.ball_y = WINDOW_HEIGHT / 2.0f;

    /* Initialize ball velocity (moving right and slightly up) */
    game_state.ball_vel_x = BALL_SPEED;
    game_state.ball_vel_y = -BALL_SPEED * 0.5f;

    /* Initialize input state */
    game_state.up_pressed = false;
    game_state.down_pressed = false;
    game_state.mouse_control = false;
    game_state.mouse_y = 0.0f;

    /* Initialize timing */
    game_state.last_time = SDL_GetTicks();
}

/* This function runs once at startup. */
SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
    SDL_SetAppMetadata("SDL3 Pong Game", "1.0", "com.example.pong");

    if (!SDL_Init(SDL_INIT_VIDEO)) {
        SDL_Log("Couldn't initialize SDL: %s", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    if (!SDL_CreateWindowAndRenderer("SDL3 Pong Game", WINDOW_WIDTH, WINDOW_HEIGHT, 0, &window, &renderer)) {
        SDL_Log("Couldn't create window/renderer: %s", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    /* Initialize game state */
    init_game_state();

    return SDL_APP_CONTINUE;  /* carry on with the program! */
}

/* This function runs when a new event (mouse input, keypresses, etc) occurs. */
SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
    if (event->type == SDL_EVENT_QUIT) {
        return SDL_APP_SUCCESS;  /* end the program, reporting success to the OS. */
    }

    /* Handle keyboard input */
    if (event->type == SDL_EVENT_KEY_DOWN) {
        switch (event->key.key) {
            case SDLK_UP:
            case SDLK_W:
                game_state.up_pressed = true;
                game_state.mouse_control = false;  /* Switch back to keyboard control */
                break;
            case SDLK_DOWN:
            case SDLK_S:
                game_state.down_pressed = true;
                game_state.mouse_control = false;  /* Switch back to keyboard control */
                break;
            case SDLK_SPACE:
                /* Reset ball position on spacebar */
                init_game_state();
                break;
        }
    }

    if (event->type == SDL_EVENT_KEY_UP) {
        switch (event->key.key) {
            case SDLK_UP:
            case SDLK_W:
                game_state.up_pressed = false;
                break;
            case SDLK_DOWN:
            case SDLK_S:
                game_state.down_pressed = false;
                break;
        }
    }

    /* Handle mouse input */
    if (event->type == SDL_EVENT_MOUSE_MOTION) {
        game_state.mouse_control = true;
        game_state.mouse_y = event->motion.y;
        /* Set target paddle position to center paddle on mouse Y */
        game_state.target_paddle_y = game_state.mouse_y - (PADDLE_HEIGHT / 2.0f);
    }

    if (event->type == SDL_EVENT_MOUSE_BUTTON_DOWN) {
        if (event->button.button == SDL_BUTTON_LEFT) {
            /* Reset ball on left mouse click */
            init_game_state();
        }
    }

    /* Handle touch input */
    if (event->type == SDL_EVENT_FINGER_DOWN || event->type == SDL_EVENT_FINGER_MOTION) {
        game_state.mouse_control = true;
        /* Convert normalized touch coordinates to screen coordinates */
        game_state.mouse_y = event->tfinger.y * WINDOW_HEIGHT;
        /* Set target paddle position to center paddle on touch Y */
        game_state.target_paddle_y = game_state.mouse_y - (PADDLE_HEIGHT / 2.0f);
    }

    if (event->type == SDL_EVENT_FINGER_UP) {
        /* Reset ball on touch release */
        init_game_state();
    }

    return SDL_APP_CONTINUE;  /* carry on with the program! */
}

/* Update game logic */
static void update_game(float delta_time)
{
    /* Store previous paddle position for swept collision detection */
    game_state.prev_paddle_y = game_state.paddle_y;

    /* Update paddle position based on input type */
    if (game_state.mouse_control) {
        /* Direct mouse/touch movement - paddle follows immediately */
        game_state.paddle_y = game_state.target_paddle_y;
    } else {
        /* Keyboard movement */
        if (game_state.up_pressed) {
            game_state.paddle_y -= PADDLE_SPEED * delta_time;
        }
        if (game_state.down_pressed) {
            game_state.paddle_y += PADDLE_SPEED * delta_time;
        }
    }

    /* Keep paddle within screen bounds */
    if (game_state.paddle_y < WALL_THICKNESS) {
        game_state.paddle_y = WALL_THICKNESS;
        game_state.target_paddle_y = game_state.paddle_y;
    }
    if (game_state.paddle_y > WINDOW_HEIGHT - PADDLE_HEIGHT - WALL_THICKNESS) {
        game_state.paddle_y = WINDOW_HEIGHT - PADDLE_HEIGHT - WALL_THICKNESS;
        game_state.target_paddle_y = game_state.paddle_y;
    }

    /* Update ball position */
    game_state.ball_x += game_state.ball_vel_x * delta_time;
    game_state.ball_y += game_state.ball_vel_y * delta_time;

    /* Ball collision with top wall */
    if (game_state.ball_y <= WALL_THICKNESS) {
        game_state.ball_y = WALL_THICKNESS;
        game_state.ball_vel_y = -game_state.ball_vel_y;
    }

    /* Ball collision with bottom wall */
    if (game_state.ball_y >= WINDOW_HEIGHT - BALL_SIZE - WALL_THICKNESS) {
        game_state.ball_y = WINDOW_HEIGHT - BALL_SIZE - WALL_THICKNESS;
        game_state.ball_vel_y = -game_state.ball_vel_y;
    }

    /* Ball collision with right wall */
    if (game_state.ball_x >= WINDOW_WIDTH - BALL_SIZE - WALL_THICKNESS) {
        game_state.ball_x = WINDOW_WIDTH - BALL_SIZE - WALL_THICKNESS;
        game_state.ball_vel_x = -game_state.ball_vel_x;
    }

    /* Ball collision with paddle using swept collision detection */
    if (check_swept_paddle_collision(game_state.ball_x, game_state.ball_y,
                                     game_state.prev_paddle_y, game_state.paddle_y)) {

        game_state.ball_x = PADDLE_WIDTH + 10;
        game_state.ball_vel_x = -game_state.ball_vel_x;

        /* Add some variation to the bounce based on where it hits the current paddle position */
        float hit_pos = (game_state.ball_y + BALL_SIZE/2 - game_state.paddle_y) / PADDLE_HEIGHT;
        /* Clamp to valid range */
        if (hit_pos < 0.0f) hit_pos = 0.0f;
        if (hit_pos > 1.0f) hit_pos = 1.0f;
        hit_pos = (hit_pos - 0.5f) * 2.0f; /* Convert to range -1 to 1 */
        game_state.ball_vel_y = hit_pos * BALL_SPEED;
    }

    /* Reset ball if it goes off the left side */
    if (game_state.ball_x < -BALL_SIZE) {
        init_game_state();
    }
}

/* Render the game */
static void render_game(void)
{
    /* Clear screen to black */
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderClear(renderer);

    /* Set draw color to white for game objects */
    SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);

    /* Draw top wall */
    SDL_FRect top_wall = {0, 0, WINDOW_WIDTH, WALL_THICKNESS};
    SDL_RenderFillRect(renderer, &top_wall);

    /* Draw right wall */
    SDL_FRect right_wall = {WINDOW_WIDTH - WALL_THICKNESS, 0, WALL_THICKNESS, WINDOW_HEIGHT};
    SDL_RenderFillRect(renderer, &right_wall);

    /* Draw bottom wall */
    SDL_FRect bottom_wall = {0, WINDOW_HEIGHT - WALL_THICKNESS, WINDOW_WIDTH, WALL_THICKNESS};
    SDL_RenderFillRect(renderer, &bottom_wall);

    /* Draw paddle */
    SDL_FRect paddle = {10, game_state.paddle_y, PADDLE_WIDTH, PADDLE_HEIGHT};
    SDL_RenderFillRect(renderer, &paddle);

    /* Draw ball */
    SDL_FRect ball = {game_state.ball_x, game_state.ball_y, BALL_SIZE, BALL_SIZE};
    SDL_RenderFillRect(renderer, &ball);

    /* Present the rendered frame */
    SDL_RenderPresent(renderer);
}

/* This function runs once per frame, and is the heart of the program. */
SDL_AppResult SDL_AppIterate(void *appstate)
{
    /* Calculate delta time */
    Uint64 current_time = SDL_GetTicks();
    float delta_time = (current_time - game_state.last_time) / 1000.0f;
    game_state.last_time = current_time;

    /* Cap delta time to prevent large jumps */
    if (delta_time > 0.016f) {
        delta_time = 0.016f;
    }

    /* Update game logic */
    update_game(delta_time);

    /* Render the game */
    render_game();

    return SDL_APP_CONTINUE;  /* carry on with the program! */
}

/* This function runs once at shutdown. */
void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
    /* SDL will clean up the window/renderer for us. */
}